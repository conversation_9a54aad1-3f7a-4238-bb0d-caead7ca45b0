import React from 'react';

interface AlertProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  onClose?: () => void;
}

const Alert: React.FC<AlertProps> = ({ message, type, onClose }) => {
  const baseClasses = "p-4 rounded-md shadow-md flex justify-between items-center";
  let typeClasses = "";

  switch (type) {
    case 'success':
      typeClasses = "bg-green-100 border border-green-400 text-green-700";
      break;
    case 'error':
      typeClasses = "bg-red-100 border border-red-400 text-red-700";
      break;
    case 'warning':
      typeClasses = "bg-yellow-100 border border-yellow-400 text-yellow-700";
      break;
    case 'info':
    default:
      typeClasses = "bg-blue-100 border border-blue-400 text-blue-700";
      break;
  }

  return (
    <div className={`${baseClasses} ${typeClasses}`} role="alert">
      <span>{message}</span>
      {onClose && (
        <button
          onClick={onClose}
          className="ml-4 text-xl font-semibold hover:opacity-75"
          aria-label="Close alert"
        >
          &times;
        </button>
      )}
    </div>
  );
};

export default Alert;