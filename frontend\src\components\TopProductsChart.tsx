import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import { ProductCategoryDistribution } from '../types';
import { CHART_COLORS } from '../constants';

interface TopProductsChartProps {
  data: ProductCategoryDistribution[];
}

const TopProductsChart: React.FC<TopProductsChartProps> = ({ data }) => {
   if (!data || data.length === 0) {
    return <p className="text-center text-gray-500">No top products data available.</p>;
  }
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data} layout="vertical" margin={{ top: 5, right: 30, left: 50, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
        <XAxis type="number" tick={{ fontSize: 12 }} tickFormatter={(value) => `$${value.toLocaleString()}`} />
        <YAxis dataKey="name" type="category" tick={{ fontSize: 10 }} width={100} interval={0}/>
        <Tooltip formatter={(value: number) => [`$${value.toLocaleString()}`, "Revenue"]}/>
        <Legend wrapperStyle={{ fontSize: "14px" }} />
        <Bar dataKey="value" name="Revenue" fill={CHART_COLORS[1]} barSize={20} />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default TopProductsChart;