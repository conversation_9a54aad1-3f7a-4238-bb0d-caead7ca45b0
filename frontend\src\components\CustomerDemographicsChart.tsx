import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { CustomerDemographicData } from '../types';
import { CHART_COLORS } from '../constants';

interface CustomerDemographicsChartProps {
  data: CustomerDemographicData[];
  title: string;
}

const CustomerDemographicsChart: React.FC<CustomerDemographicsChartProps> = ({ data, title }) => {
  if (!data || data.length === 0) {
    return <p className="text-center text-gray-500">No {title.toLowerCase()} data available.</p>;
  }
  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
          nameKey="name"
          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
        >
          {data.map((_entry, index) => (
            <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value: number, name: string) => [value, name]}/>
        <Legend wrapperStyle={{ fontSize: "14px", marginTop: "10px" }} />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default CustomerDemographicsChart;