<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI E-commerce Analytics</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { margin: 0; font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  </style>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "recharts": "https://esm.sh/recharts@^2.15.3",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "@nestjs/core": "https://esm.sh/@nestjs/core@^11.1.2",
    "@nestjs/common": "https://esm.sh/@nestjs/common@^11.1.2",
    "dotenv": "https://esm.sh/dotenv@^16.5.0",
    "@nestjs/config": "https://esm.sh/@nestjs/config@^4.0.2",
    "@nestjs/mongoose": "https://esm.sh/@nestjs/mongoose@^11.0.3",
    "class-validator": "https://esm.sh/class-validator@^0.14.2",
    "@nestjs/passport": "https://esm.sh/@nestjs/passport@^11.0.5",
    "rxjs": "https://esm.sh/rxjs@^7.8.2",
    "passport-jwt": "https://esm.sh/passport-jwt@^4.0.1",
    "@nestjs/jwt": "https://esm.sh/@nestjs/jwt@^11.0.0",
    "mongoose": "https://esm.sh/mongoose@^8.15.1",
    "bcrypt": "https://esm.sh/bcrypt@^6.0.0",
    "class-transformer": "https://esm.sh/class-transformer@^0.5.1",
    "@google/genai": "https://esm.sh/@google/genai@^1.4.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>