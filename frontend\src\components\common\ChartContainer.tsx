import React from 'react';

interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const ChartContainer: React.FC<ChartContainerProps> = ({ title, children, className = '' }) => {
  return (
    <div className={`bg-white p-6 rounded-xl shadow-lg ${className}`}>
      <h3 className="text-xl font-semibold text-gray-700 mb-4">{title}</h3>
      <div className="h-72 md:h-80 w-full"> {/* Fixed height for chart consistency */}
        {children}
      </div>
    </div>
  );
};

export default ChartContainer;