
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

export type AuditLogDocument = AuditLog & Document;

@Schema({
  timestamps: { createdAt: 'createdAt', updatedAt: false }, // Use standard 'createdAt' field
   toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  }
})
export class AuditLog {
  @Prop({ type: String, virtual: true, get() { return this._id?.toHexString(); } })
  id: string;
  
  // createdAt is handled by Mongoose timestamps option
  createdAt?: Date;

  @Prop({ required: true, type: String }) // Could be SchemaTypes.ObjectId, ref: 'User' if you want population
  userId: string;

  @Prop({ type: String })
  userName?: string; // Denormalized

  @Prop({ required: true, type: String })
  action: string;

  @Prop({ required: true, type: String })
  details: string;
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);
AuditLogSchema.virtual('timestamp').get(function(this: AuditLogDocument) {
  return this.createdAt; // Use the createdAt field from timestamps
});
