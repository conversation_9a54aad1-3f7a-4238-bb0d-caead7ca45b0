import React from 'react';
import { Product } from '../types';
import ProductCard from './ProductCard';
import Spinner from './common/Spinner';

interface RecommendationSectionProps {
  title: string;
  products: Product[];
  loading: boolean;
}

const RecommendationSection: React.FC<RecommendationSectionProps> = ({ title, products, loading }) => {
  if (loading) {
    return (
      <div className="py-6">
        <h3 className="text-xl font-semibold text-gray-700 mb-4">{title}</h3>
        <Spinner />
      </div>
    );
  }

  if (products.length === 0) {
    return (
       <div className="py-6">
        <h3 className="text-xl font-semibold text-gray-700 mb-4">{title}</h3>
        <p className="text-gray-500">No recommendations available at the moment.</p>
      </div>
    );
  }

  return (
    <div className="py-6">
      <h3 className="text-xl font-semibold text-gray-700 mb-4">{title}</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
};

export default RecommendationSection;