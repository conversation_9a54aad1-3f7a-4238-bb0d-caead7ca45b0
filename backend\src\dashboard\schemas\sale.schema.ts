import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { Customer } from './customer.schema';

export type SaleDocument = Sale & Document;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class Sale {
  @Prop({ type: String, virtual: true, get() { return this._id?.toHexString(); } })
  id: string;

  @Prop({ type: String, required: true, index: true })
  productId: string;

  @Prop({ type: String, required: true })
  productName: string; // Denormalized for query performance

  @Prop({ type: SchemaTypes.ObjectId, ref: Customer.name, required: true, index: true })
  customerId: string;

  @Prop({ type: String, required: true })
  customerName: string; // Denormalized

  @Prop({ type: Date, required: true, default: Date.now, index: true })
  date: Date;

  @Prop({ type: Number, required: true, min: 1 })
  quantity: number;

  @Prop({ type: Number, required: true, min: 0 })
  pricePerUnit: number; // Price at the time of sale

  @Prop({ type: Number, required: true, min: 0 })
  totalAmount: number;
}

export const SaleSchema = SchemaFactory.createForClass(Sale);
