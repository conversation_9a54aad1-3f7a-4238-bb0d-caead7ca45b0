import { Injectable, UnauthorizedException, InternalServerErrorException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from './schemas/user.schema';
import { JwtPayload, UserRole } from '../shared/types.shared';
import { UserResponseDto } from './dto/login.dto';
import { AuditService } from '../audit/audit.service';
import * as bcrypt from 'bcrypt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private jwtService: JwtService,
    private auditService: AuditService,
    private configService: ConfigService, // Injected ConfigService
  ) {}

  async validateUser(email: string, pass: string): Promise<UserDocument | null> {
    const user = await this.userModel.findOne({ email }).select('+password').exec();
    if (user && user.password && (await (user as UserDocument).comparePassword(pass))) {
      return user;
    }
    return null;
  }
  
  async validateUserPayload(payload: JwtPayload): Promise<UserDocument | null> {
    this.logger.debug(`Validating JWT payload: ${JSON.stringify(payload)}`);
    
    if (!payload.sub) {
      this.logger.error('JWT payload missing sub field');
      return null;
    }
    
    try {
      // Check if payload.sub is a valid MongoDB ObjectId
      if (!/^[0-9a-fA-F]{24}$/.test(payload.sub)) {
        this.logger.error(`Invalid MongoDB ObjectId format in JWT sub: ${payload.sub}`);
        return null;
      }
      
      const user = await this.userModel.findById(payload.sub).exec();
      
      if (!user) {
        this.logger.error(`No user found with ID: ${payload.sub}`);
        return null;
      }
      
      if (user.email !== payload.email) {
        this.logger.error(`Email mismatch: ${user.email} vs ${payload.email}`);
        return null;
      }
      
      if (user.role !== payload.role) {
        this.logger.error(`Role mismatch: ${user.role} vs ${payload.role}`);
        return null;
      }
      
      this.logger.debug(`User validated successfully: ${user.email}`);
      return user;
    } catch (error) {
      this.logger.error(`Error validating user from JWT: ${error.message}`);
      return null;
    }
  }

  async login(userDoc: UserDocument) {
    const user = userDoc.toObject({ virtuals: true }) as User;

    // Add debugging to check if id is properly set
    this.logger.debug(`User object after toObject: ${JSON.stringify(user)}`);
    this.logger.debug(`User ID: ${user.id}, Type: ${typeof user.id}`);

    // Ensure we have a valid ID
    if (!user.id) {
      this.logger.error('User ID is missing or invalid in login method');
      throw new Error('Cannot create JWT token: No valid user ID available');
    }

    // Validate that the ID is a valid MongoDB ObjectId
    if (!/^[0-9a-fA-F]{24}$/.test(user.id)) {
      this.logger.error(`Invalid MongoDB ObjectId format for user.id: ${user.id}`);
      throw new Error('Cannot create JWT token: Invalid user ID format');
    }
    
    const payload: JwtPayload = { sub: user.id, email: user.email, role: user.role };
    this.logger.debug(`JWT Payload: ${JSON.stringify(payload)}`);
    
    const userResponse = new UserResponseDto(user); 

    try {
      await this.auditService.create(user.id, 'User Login', `User ${user.email} logged in successfully.`, user.name);
    } catch (auditError) {
        this.logger.error(`Failed to create audit log for login: ${auditError.message}`);
    }

    return {
      user: userResponse,
      token: this.jwtService.sign(payload),
    };
  }

  async getUserFromToken(token: string): Promise<UserResponseDto | null> {
    try {
      // Clean the token if it has the Bearer prefix
      const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;
      
      this.logger.debug(`Verifying token in getUserFromToken`);
      const payload = this.jwtService.verify<JwtPayload>(cleanToken);
      
      if (!payload || !payload.sub) {
        this.logger.error('Invalid payload structure in token');
        return null;
      }
      
      this.logger.debug(`Looking up user with ID: ${payload.sub}`);
      
      // Check if payload.sub is a valid MongoDB ObjectId
      if (!/^[0-9a-fA-F]{24}$/.test(payload.sub)) {
        this.logger.error(`Invalid MongoDB ObjectId format in JWT sub: ${payload.sub}`);
        return null;
      }
      
      const userDoc = await this.userModel.findById(payload.sub).exec();
      
      if (!userDoc) {
        this.logger.error(`No user found with ID: ${payload.sub}`);
        return null;
      }
      
      const user = userDoc.toObject({ virtuals: true }) as User;

      // Verify ID is properly set (should be handled by virtual now)
      if (!user.id) {
        this.logger.error('User object missing ID after toObject conversion');
        return null;
      }
      
      this.logger.debug(`User found from token: ${user.email}`);
      return new UserResponseDto(user);
    } catch (error) {
      this.logger.warn(`Error verifying token in getUserFromToken: ${error.message}`);
      return null;
    }
  }

  async ensureAdminUserExists(): Promise<void> {
    const adminEmail = this.configService.get<string>('DEFAULT_ADMIN_EMAIL');
    const adminPassword = this.configService.get<string>('DEFAULT_ADMIN_PASSWORD');

    if (!adminEmail || !adminPassword) {
        this.logger.error('DEFAULT_ADMIN_EMAIL or DEFAULT_ADMIN_PASSWORD not configured in .env file in the backend directory. Cannot create default admin user.');
        return;
    }

    const existingAdmin = await this.userModel.findOne({ email: adminEmail }).exec();
    if (!existingAdmin) {
      this.logger.log(`No admin user found with email ${adminEmail}. Creating default admin.`);
      const adminUser = new this.userModel({
        name: 'Default Admin',
        email: adminEmail,
        password: adminPassword, // Password will be hashed by pre-save hook
        role: UserRole.ADMIN,
      });
      try {
        const savedAdmin = await adminUser.save();
        this.logger.log(`Default admin user ${adminEmail} created successfully.`);
        await this.auditService.create(savedAdmin.id, 'Admin User Created', `Default admin user ${adminEmail} created via system initialization.`, savedAdmin.name);
      } catch (error) {
        this.logger.error(`Failed to create default admin user: ${error.message}`, error.stack);
      }
    } else {
         this.logger.log(`Admin user ${adminEmail} already exists.`);
    }
  }

  async onModuleInit() {
    await this.ensureAdminUserExists();
  }
}