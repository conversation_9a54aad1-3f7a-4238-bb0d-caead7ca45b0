
import { Controller, Get, Param, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductDto, HistoricalSalesDto } from './dto/product.dto';
import { Product } from './entities/product.entity'; // Import Product entity type for return consistency
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('products')
// @UseGuards(JwtAuthGuard) // Apply to all routes in this controller if needed, or per route
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get()
  async findAll(): Promise<ProductDto[]> { // Return Promise<ProductDto[]>
    // Assuming Product schema type is compatible with ProductDto
    return await this.productsService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<ProductDto> { // Return Promise<ProductDto>
    // Note: If your product IDs are UUIDs, use @Param('id', ParseUUIDPipe)
    // For mock data, string is fine.
    // Assuming Product schema type is compatible with ProductDto
    return await this.productsService.findOne(id);
  }

  @UseGuards(JwtAuthGuard) // Example of protecting a specific route
  @Get(':id/historical-sales')
  async findHistoricalSales(@Param('id') id: string): Promise<HistoricalSalesDto[]> { // Return Promise<HistoricalSalesDto[]>
    return await this.productsService.findHistoricalSales(id);
  }
}
