import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { User, UserRole } from '../../shared/types.shared';

export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6) // Example: enforce minimum password length
  password: string;
}

export class LoginResponseDto {
  user: UserResponseDto;
  token: string;
}

export class UserResponseDto {
  id: string;
  name: string;
  email: string;
  role: UserRole;

  constructor(user: User) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.role = user.role;
  }
}
