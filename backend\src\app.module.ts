import { Module } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { ProductsModule } from './products/products.module';
import { AiModule } from './ai/ai.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DashboardModule } from './dashboard/dashboard.module';
import { InventoryModule } from './inventory/inventory.module';
import { AdminModule } from './admin/admin.module';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditModule } from './audit/audit.module';
import { Product } from './products/entities/product.entity';
import { Inventory } from './inventory/entities/inventory.entity';
import { Sale } from './dashboard/entities/sale.entity';
import { Demographic } from './dashboard/entities/demographic.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, 
      envFilePath: '.env', // Path relative to backend directory (CWD when running backend)
    }),
    // MongoDB configuration (for auth/users, admin settings, and audit logs)
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const dbName = configService.get<string>('MONGODB_DATABASE') || 'userDataBase';
        const uri = configService.get<string>('MONGODB_URI');
        
        // Debug logging to see what values we're getting
        console.log('=== MONGODB CONFIGURATION DEBUG ===');
        console.log('MONGODB_DATABASE from env:', configService.get<string>('MONGODB_DATABASE'));
        console.log('Final dbName being used:', dbName);
        console.log('MONGODB_URI from env:', uri);
        console.log('=====================================');
        
        // Build the complete URI with the database name
        const fullUri = `${uri}/${dbName}`;
        
        console.log(`Attempting to connect to MongoDB: ${fullUri}`);
        
        return {
          uri: fullUri,
          // Explicitly set the database name
          dbName: dbName,
          // Connection options
          retryWrites: true,
          w: 'majority',
          // Add connection event handlers
          connectionFactory: (connection) => {
            connection.on('connected', () => {
              console.log(`MongoDB successfully connected to database: ${connection.db.databaseName}`);
            });
            connection.on('error', (error) => {
              console.error('MongoDB connection error:', error);
            });
            connection.on('disconnected', () => {
              console.log('MongoDB disconnected');
            });
            return connection;
          }
        };
      },
      inject: [ConfigService],
    }),
    // PostgreSQL configuration (for products, inventory, sales, demographics)
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const dbName = configService.get<string>('POSTGRES_DATABASE');
        return {
          type: 'postgres',
          host: configService.get<string>('POSTGRES_HOST'),
          port: configService.get<number>('POSTGRES_PORT'),
          username: configService.get<string>('POSTGRES_USERNAME'),
          password: configService.get<string>('POSTGRES_PASSWORD'),
          database: dbName,
          entities: [
            Product, 
            Inventory, 
            Sale, 
            Demographic
          ],
          synchronize: false, // Database structure already exists
          // Add connection event handlers
          connectTimeoutMS: 10000,
          // Log when connection is established
          entityPrefix: '',
          applicationName: 'e-commerce-analytics-dashboard',
          // Log when connected
          logging: true,
          logger: {
            log: (level, message) => {
              if (level === 'log' && message.includes('database')) {
                console.log(`PostgreSQL connected to database: ${dbName}`);
              }
            },
            logQuery: () => {},
            logQueryError: (error) => console.error('PostgreSQL query error:', error),
            logQuerySlow: () => {},
            logSchemaBuild: () => {},
            logMigration: () => {},
          }
        };
      },
      inject: [ConfigService],
    }),
    AuthModule,
    ProductsModule,
    AiModule,
    DashboardModule, 
    InventoryModule,   
    AdminModule,
    AuditModule, // AuditModule is Global, so it's available everywhere
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}