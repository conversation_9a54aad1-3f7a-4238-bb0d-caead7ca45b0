import { Controller, Post, Body, UseGuards, ValidationPipe } from '@nestjs/common';
import { AiService } from './ai.service';
import { SalesForecastRequestDto, ProductRecommendationsRequestDto, AnalyticsInsightsRequestDto } from './dto/ai-request.dto';
import { Product, ForecastDataPoint, AISearchResult } from '../shared/types.shared';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';

@Controller('ai')
@UseGuards(JwtAuthGuard) // Protect all AI routes
export class AiController {
  constructor(private readonly aiService: AiService) {}

  @Post('forecast')
  async getSalesForecast(
    @Body(new ValidationPipe()) forecastDto: SalesForecastRequestDto
  ): Promise<ForecastDataPoint[]> {
    return this.aiService.getSalesForecast(forecastDto);
  }

  @Post('recommendations')
  async getProductRecommendations(
    @Body(new ValidationPipe()) recommendationsDto: ProductRecommendationsRequestDto
  ): Promise<Product[]> {
    return this.aiService.getProductRecommendations(recommendationsDto);
  }

  @Post('insights')
  @UseGuards(AdminRoleGuard) // Only Admins can use this
  async searchAnalyticsInsights(
    @Body(new ValidationPipe()) insightsDto: AnalyticsInsightsRequestDto
  ): Promise<AISearchResult> {
    return this.aiService.searchAnalyticsInsights(insightsDto);
  }
}
