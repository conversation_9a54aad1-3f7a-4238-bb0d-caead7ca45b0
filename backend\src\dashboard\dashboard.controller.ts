
import { Controller, Get, Query, UseGuards, ParseIntPipe, DefaultValuePipe } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SalesTrendData, ProductCategoryDistribution } from '../shared/types.shared';
import { DashboardMetricsDto, CustomerDemographicsResponseDto } from './dashboard.dto';

@Controller('dashboard')
@UseGuards(JwtAuthGuard)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('metrics')
  async getMetrics(): Promise<DashboardMetricsDto> {
    return await this.dashboardService.getMetrics();
  }

  @Get('sales-trends')
  async getSalesTrends(): Promise<SalesTrendData[]> {
    return await this.dashboardService.getSalesTrends();
  }

  @Get('top-products')
  async getTopProducts(
    @Query('limit', new DefaultValuePipe(5), ParseIntPipe) limit: number,
  ): Promise<ProductCategoryDistribution[]> {
    return await this.dashboardService.getTopProducts(limit);
  }

  @Get('customer-demographics')
  async getCustomerDemographics(): Promise<CustomerDemographicsResponseDto> {
    return await this.dashboardService.getCustomerDemographics();
  }
}
