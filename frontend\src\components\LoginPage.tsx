import React, { useState } from 'react';
import { login, LoginResponse } from '../services/authService';
import Alert from './common/Alert';
import Spinner from './common/Spinner';
import { APP_NAME } from '../constants';

interface LoginPageProps {
  onLoginSuccess: (data: LoginResponse) => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    if (!email || !password) {
      setError('Please enter both email and password.');
      return;
    }
    setLoading(true);
    try {
      const loginData = await login(email, password);
      onLoginSuccess(loginData);
    } catch (err: any) {
      setError(err.message || 'Lo<PERSON> failed. Please check your credentials and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-700 flex flex-col justify-center items-center p-4">
      <div className="max-w-md w-full bg-white shadow-2xl rounded-xl p-8 space-y-6">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-blue-600">{APP_NAME}</h1>
          <p className="mt-2 text-gray-600">Sign in to access your dashboard</p>
        </div>

        {error && <Alert message={error} type="error" onClose={() => setError(null)} />}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="••••••••"
            />
          </div>
          <div className="text-sm text-right">
            <span className="font-medium text-blue-600 hover:text-blue-500">
              (default admin: <EMAIL> / StrongAdminP@ssw0rd!)
            </span>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
            >
              {loading ? <Spinner /> : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
       <footer className="mt-8 text-center text-sm text-gray-400">
        &copy; {new Date().getFullYear()} {APP_NAME}. All rights reserved.
      </footer>
    </div>
  );
};

export default LoginPage;