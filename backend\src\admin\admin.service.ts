

import { Injectable, NotFoundException, BadRequestException, Logger, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from '../auth/schemas/user.schema';
import { SystemSettings, SystemSettingsDocument } from './schemas/system-settings.schema';
import { UserRole } from '../shared/types.shared';
import { UserAdminResponseDto, SystemSettingsDto, AuditLogEntryDto } from './dto/admin.dto'; // Ensure AuditLogEntryDto is used if AuditLog[] is returned
import { AuditService } from '../audit/audit.service';
import { AuditLog, AuditLogDocument } from '../audit/schemas/audit-log.schema'; // Use AuditLogDocument for type consistency


@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(SystemSettings.name) private systemSettingsModel: Model<SystemSettingsDocument>,
    private auditService: AuditService,
  ) {}

  async getAllUsers(): Promise<UserAdminResponseDto[]> {
    const users = await this.userModel.find().exec();
    return users.map(user => new UserAdminResponseDto(user.toObject({ virtuals: true }) as User));
  }

  async updateUserRole(userId: string, newRole: UserRole, currentAdminId: string): Promise<UserAdminResponseDto> {
    // Validate input parameters
    if (!userId) {
      throw new BadRequestException('User ID is required.');
    }
    if (!currentAdminId) {
      throw new BadRequestException('Current admin ID is required.');
    }
    if (!newRole) {
      throw new BadRequestException('New role is required.');
    }

    this.logger.debug(`Updating user role: userId=${userId}, newRole=${newRole}, currentAdminId=${currentAdminId}`);

    const userToUpdate = await this.userModel.findById(userId).exec();
    if (!userToUpdate) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    if (userToUpdate.id === currentAdminId && newRole !== UserRole.ADMIN) {
      throw new BadRequestException('Cannot remove admin role from yourself.');
    }

    const oldRole = userToUpdate.role;
    userToUpdate.role = newRole;
    await userToUpdate.save();

    try {
      await this.auditService.create(currentAdminId, 'User Role Changed', `Role for user ${userToUpdate.email} (ID: ${userId}) changed from ${oldRole} to ${newRole}.`, userToUpdate.name);
    } catch (auditError) {
      this.logger.error(`Failed to create audit log for role change: ${auditError.message}`, auditError.stack);
      // Don't fail the entire operation if audit logging fails, but log the error
      this.logger.warn(`Role change completed but audit log failed for user ${userId}`);
    }

    return new UserAdminResponseDto(userToUpdate.toObject({ virtuals: true }) as User);
  }

  async deleteUser(userId: string, currentAdminId: string): Promise<void> {
    // Validate input parameters
    if (!userId) {
      throw new BadRequestException('User ID is required.');
    }
    if (!currentAdminId) {
      throw new BadRequestException('Current admin ID is required.');
    }

    this.logger.debug(`Deleting user: userId=${userId}, currentAdminId=${currentAdminId}`);

    const userToDelete = await this.userModel.findById(userId).exec();
    if (!userToDelete) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    if (userToDelete.id === currentAdminId) {
      throw new BadRequestException('Cannot delete yourself.');
    }

    const deletedUserEmail = userToDelete.email;
    const deletedUserName = userToDelete.name;
    await this.userModel.deleteOne({ _id: userId }).exec();

    try {
      await this.auditService.create(currentAdminId, 'User Deleted', `User ${deletedUserEmail} (ID: ${userId}) deleted.`, deletedUserName);
    } catch (auditError) {
      this.logger.error(`Failed to create audit log for user deletion: ${auditError.message}`, auditError.stack);
      // Don't fail the entire operation if audit logging fails, but log the error
      this.logger.warn(`User deletion completed but audit log failed for user ${userId}`);
    }
  }

  async getSystemSettings(): Promise<SystemSettingsDto> {
    // System settings are usually a single document in a collection
    let settings = await this.systemSettingsModel.findOne().exec();
    if (!settings) {
      this.logger.warn('No system settings found in DB. Creating default settings.');
      settings = new this.systemSettingsModel(); // Creates with default values from schema
      await settings.save();
      await this.auditService.create('system-event', 'System Settings Initialized', 'Default system settings were created.');
    }
    return new SystemSettingsDto(settings.toObject());
  }

  async updateSystemSettings(settingsDto: SystemSettingsDto, adminUserId: string): Promise<SystemSettingsDto> {
    // Validate input parameters
    if (!adminUserId) {
      throw new BadRequestException('Admin user ID is required.');
    }
    if (!settingsDto) {
      throw new BadRequestException('Settings data is required.');
    }

    this.logger.debug(`Updating system settings: adminUserId=${adminUserId}`);

    let settings = await this.systemSettingsModel.findOne().exec();
    if (!settings) {
      // This case should ideally be handled by getSystemSettings creating it first,
      // but as a fallback, create if somehow still missing.
      settings = new this.systemSettingsModel(settingsDto);
    } else {
      settings.maintenanceMode = settingsDto.maintenanceMode;
      settings.aiServiceEndpoint = settingsDto.aiServiceEndpoint;
      settings.maxRecommendations = settingsDto.maxRecommendations;
    }
    const updatedSettings = await settings.save();

    try {
      await this.auditService.create(adminUserId, 'System Settings Updated', `System settings updated: ${JSON.stringify(settingsDto)}`);
    } catch (auditError) {
      this.logger.error(`Failed to create audit log for settings update: ${auditError.message}`, auditError.stack);
      // Don't fail the entire operation if audit logging fails, but log the error
      this.logger.warn(`Settings update completed but audit log failed`);
    }

    return new SystemSettingsDto(updatedSettings.toObject());
  }

  async getAuditLogs(searchTerm?: string): Promise<AuditLogEntryDto[]> {
     // Use AuditService, which now handles DB querying
    const logs: AuditLogDocument[] = await this.auditService.findAll(100, searchTerm); // Get last 100 or filtered
    return logs.map(log => new AuditLogEntryDto(log.toObject({ virtuals: true })));
  }

   // Ensure default settings are created on module init if they don't exist
  async onModuleInit() {
    await this.getSystemSettings(); // This will create defaults if none exist
  }
}