import React, { useState, useEffect } from 'react';
import { InventoryItem } from '../types';
import Spinner from './common/Spinner';

interface RestockInventoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: {
    productId: string;
    additionalStock: number;
    restockDate: string;
  }) => void;
  isLoading: boolean;
  inventoryItems: InventoryItem[];
}

const RestockInventoryModal: React.FC<RestockInventoryModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
  inventoryItems
}) => {
  const [formData, setFormData] = useState({
    productId: '',
    additionalStock: 0,
    restockDate: new Date().toISOString().split('T')[0]
  });

  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

  useEffect(() => {
    if (formData.productId) {
      const item = inventoryItems.find(item => item.productId === formData.productId);
      setSelectedItem(item || null);
    } else {
      setSelectedItem(null);
    }
  }, [formData.productId, inventoryItems]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.productId || formData.additionalStock <= 0) {
      alert('Please select a product and enter a valid stock quantity');
      return;
    }
    onSubmit(formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'additionalStock' 
        ? parseInt(value) || 0 
        : value
    }));
  };

  const resetForm = () => {
    setFormData({
      productId: '',
      additionalStock: 0,
      restockDate: new Date().toISOString().split('T')[0]
    });
    setSelectedItem(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Restock Inventory</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
            disabled={isLoading}
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="productId" className="block text-sm font-medium text-gray-700 mb-1">
              Select Product to Restock
            </label>
            <select
              id="productId"
              name="productId"
              value={formData.productId}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            >
              <option value="">Select a product</option>
              {inventoryItems.map(item => (
                <option key={item.productId} value={item.productId}>
                  {item.productName} (Current: {item.stockQuantity})
                </option>
              ))}
            </select>
          </div>

          {selectedItem && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-gray-600 mb-1">Current Stock Information:</p>
              <p className="font-medium text-gray-800">{selectedItem.productName}</p>
              <p className="text-sm text-gray-600">
                Current Stock: <span className="font-medium text-blue-600">{selectedItem.stockQuantity}</span>
              </p>
              <p className="text-sm text-gray-600">
                Low Stock Threshold: <span className="font-medium">{selectedItem.lowStockThreshold}</span>
              </p>
              <p className="text-sm text-gray-600">
                Last Restocked: <span className="font-medium">{new Date(selectedItem.lastRestockDate).toLocaleDateString()}</span>
              </p>
            </div>
          )}

          <div>
            <label htmlFor="additionalStock" className="block text-sm font-medium text-gray-700 mb-1">
              Additional Stock Quantity
            </label>
            <input
              type="number"
              id="additionalStock"
              name="additionalStock"
              value={formData.additionalStock}
              onChange={handleInputChange}
              min="1"
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              placeholder="Enter quantity to add"
            />
            {selectedItem && formData.additionalStock > 0 && (
              <p className="text-sm text-green-600 mt-1">
                New total will be: <span className="font-medium">{selectedItem.stockQuantity + formData.additionalStock}</span>
              </p>
            )}
          </div>

          <div>
            <label htmlFor="restockDate" className="block text-sm font-medium text-gray-700 mb-1">
              Restock Date
            </label>
            <input
              type="date"
              id="restockDate"
              name="restockDate"
              value={formData.restockDate}
              onChange={handleInputChange}
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.productId || formData.additionalStock <= 0}
              className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 flex items-center justify-center"
            >
              {isLoading ? <Spinner /> : 'Restock'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RestockInventoryModal;
