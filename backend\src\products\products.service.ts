
import { Injectable, NotFoundException, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { HistoricalSalesDto } from './dto/product.dto';
// For historical sales, we'll need a separate Sales model or collection in a real app.
// For now, this will be a placeholder or return empty.

@Injectable()
export class ProductsService implements OnModuleInit {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  async findAll(): Promise<Product[]> {
    return this.productRepository.find();
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productRepository.findOne({ where: { id } });
    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }
    return product;
  }

  async create(productData: {
    id: string;
    name: string;
    category: string;
    price: number;
    imageUrl?: string;
    description?: string;
  }): Promise<Product> {
    const newProduct = this.productRepository.create(productData);
    return this.productRepository.save(newProduct);
  }
  
  async findHistoricalSales(productId: string): Promise<HistoricalSalesDto[]> {
    // Placeholder: In a real application, historical sales data would come from a Sales collection/model
    // related to products. For this phase, we'll return an empty array or mock if needed.
    const product = await this.productRepository.findOne({ where: { id: productId } });
    if (!product) {
      throw new NotFoundException(`Product with ID "${productId}" not found, cannot fetch historical sales.`);
    }
    // Example mock data structure - replace with actual data source
    const mockSales: HistoricalSalesDto[] = [
        { date: '2023-01-01', sales: Math.floor(Math.random() * 50) + 10 },
        { date: '2023-02-01', sales: Math.floor(Math.random() * 50) + 10 },
        { date: '2023-03-01', sales: Math.floor(Math.random() * 50) + 10 },
    ];
    console.warn(`Historical sales for product ${productId} is using placeholder data.`);
    return mockSales; 
  }

  // Example: Seed products if table is empty
  async onModuleInit() {
    const count = await this.productRepository.count();
    if (count === 0) {
      console.log('No products found. Seeding initial products...');
      const initialProducts = [
        { id: 'prod_001', name: 'Smartwatch Series X', category: 'Electronics', price: 299.99, imageUrl: 'https://via.placeholder.com/300x300.png?text=Smartwatch+X', description: 'Latest gen smartwatch.' },
        { id: 'prod_002', name: 'Organic Coffee Beans', category: 'Groceries', price: 19.99, imageUrl: 'https://via.placeholder.com/300x300.png?text=Coffee+Beans', description: 'Premium Arabica beans.' },
        { id: 'prod_003', name: 'Wireless Headphones', category: 'Electronics', price: 149.50, imageUrl: 'https://via.placeholder.com/300x300.png?text=Headphones', description: 'Noise-cancelling.' },
      ];
      await this.productRepository.save(initialProducts);
      console.log('Initial products seeded.');
    }
  }
}
