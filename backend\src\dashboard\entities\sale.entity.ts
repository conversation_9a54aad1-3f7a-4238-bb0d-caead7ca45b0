import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from '../../products/entities/product.entity';

@Entity('sales')
export class Sale {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'product_id', type: 'varchar' })
  productId: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'integer' })
  sales: number;

  @Column({ type: 'varchar', nullable: true })
  salesperson: string;

  @Column({ name: 'salesperson_email', type: 'varchar', nullable: true })
  salespersonEmail: string;

  // Relation to Product
  @ManyToOne(() => Product)
  @JoinColumn({ name: 'product_id' })
  product: Product;
}