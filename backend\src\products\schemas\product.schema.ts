
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ProductDocument = Product & Document;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
     transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  }
})
export class Product {
  @Prop({ type: String, virtual: true, get() { return this._id?.toHexString(); } })
  id: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ required: true, type: String })
  category: string;

  @Prop({ required: true, type: Number })
  price: number;

  @Prop({ required: true, type: String })
  imageUrl: string;

  @Prop({ type: String })
  description?: string;
}

export const ProductSchema = SchemaFactory.createForClass(Product);
ProductSchema.index({ name: 'text', category: 'text' }); // Example index
