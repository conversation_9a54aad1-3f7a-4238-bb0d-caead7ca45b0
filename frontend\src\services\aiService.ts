import { Product, ForecastDataPoint, AISearchResult } from '../types';
import { HistoricalSalesData } from './dataService'; // Import from dataService for consistency
import { post } from './apiHelper';

const getToken = (): string | undefined => {
    const token = localStorage.getItem('authToken');
    return token ? token : undefined;
}

interface SalesForecastRequest {
  productId: string;
  productName: string; 
  productDescription?: string; 
  historicalSales: HistoricalSalesData[];
}

export const getSalesForecast = async (
  product: Product,
  historicalData: HistoricalSalesData[]
): Promise<ForecastDataPoint[]> => {
  const requestBody: SalesForecastRequest = {
    productId: product.id,
    productName: product.name,
    productDescription: product.description,
    historicalSales: historicalData,
  };
  // The backend /ai/forecast endpoint returns only the forecasted points.
  // The frontend will combine these with actual historical data.
  const forecastedPoints = await post<SalesForecastRequest, ForecastDataPoint[]>('/ai/forecast', requestBody, getToken());
  return forecastedPoints;
};

interface ProductRecommendationsRequest {
  productId: string;
  productName: string; 
  productDescription?: string; 
  category: string; 
  recommendationType: 'frequently_bought_together' | 'customers_also_viewed';
}

export const getProductRecommendations = async (
  currentProduct: Product,
  recommendationType: 'frequently_bought_together' | 'customers_also_viewed' = 'frequently_bought_together'
): Promise<Product[]> => {
  const requestBody: ProductRecommendationsRequest = {
    productId: currentProduct.id,
    productName: currentProduct.name,
    productDescription: currentProduct.description,
    category: currentProduct.category,
    recommendationType: recommendationType,
  };
  return post<ProductRecommendationsRequest, Product[]>('/ai/recommendations', requestBody, getToken());
};

interface AnalyticsInsightsRequest {
  query: string;
}

export const searchAnalyticsInsights = async (query: string): Promise<AISearchResult> => {
  const requestBody: AnalyticsInsightsRequest = { query };
  return post<AnalyticsInsightsRequest, AISearchResult>('/ai/insights', requestBody, getToken());
};