
import { IsString, <PERSON>NotEmpty, <PERSON>N<PERSON>ber, IsDateString, Min, IsOptional } from 'class-validator';

export class InventoryDto {
  @IsString()
  @IsNotEmpty()
  product_id: string;

  @IsNumber()
  @Min(0)
  stock_quantity: number;

  @IsNumber()
  @Min(0)
  lowStockThreshold: number;

  @IsDateString()
  lastRestockDate: string;
}

export class CreateInventoryDto {
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsNumber()
  @Min(0)
  stockQuantity: number;

  @IsNumber()
  @Min(0)
  lowStockThreshold: number;

  @IsDateString()
  @IsOptional()
  lastRestockDate?: string;
}

export class CreateProductWithInventoryDto {
  // Product information
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsString()
  @IsNotEmpty()
  productName: string;

  @IsString()
  @IsNotEmpty()
  category: string;

  @IsNumber()
  @Min(0)
  price: number;

  @IsString()
  @IsOptional()
  imageUrl?: string;

  @IsString()
  @IsOptional()
  description?: string;

  // Inventory information
  @IsNumber()
  @Min(0)
  stockQuantity: number;

  @IsNumber()
  @Min(0)
  lowStockThreshold: number;

  @IsDateString()
  @IsOptional()
  lastRestockDate?: string;
}

export class UpdateInventoryDto {
  @IsNumber()
  @Min(0)
  @IsOptional()
  stockQuantity?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  lowStockThreshold?: number;

  @IsDateString()
  @IsOptional()
  lastRestockDate?: string;
}

export class RestockInventoryDto {
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsNumber()
  @Min(1)
  additionalStock: number;

  @IsDateString()
  @IsOptional()
  restockDate?: string;
}
