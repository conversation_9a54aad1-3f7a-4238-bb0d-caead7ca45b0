
import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsEnum, IsBoolean, IsN<PERSON>ber, <PERSON>, <PERSON>, IsOptional, IsEmail } from 'class-validator';
import { UserR<PERSON>, User as SharedUser, SystemSettings as SharedSystemSettings } from '../../shared/types.shared'; // Use shared types for constructor

export class UserAdminResponseDto {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsEnum(UserRole)
  role: UserRole;

  constructor(user: Partial<SharedUser>) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.role = user.role;
  }
}

export class UpdateUserRoleDto {
  @IsEnum(UserRole)
  @IsNotEmpty()
  role: UserRole;
}

export class SystemSettingsDto {
  @IsBoolean()
  maintenanceMode: boolean;

  @IsString()
  @IsNotEmpty()
  aiServiceEndpoint: string;

  @IsNumber()
  @Min(1)
  @Max(10) // Example range
  maxRecommendations: number;

  constructor(settings: Partial<SharedSystemSettings>) {
    this.maintenanceMode = settings.maintenanceMode;
    this.aiServiceEndpoint = settings.aiServiceEndpoint;
    this.maxRecommendations = settings.maxRecommendations;
  }
}

export class AuditLogEntryDto {
  @IsString()
  id: string;
  
  @IsString()
  timestamp: string;
  
  @IsString()
  userId: string;
  
  @IsString()
  @IsOptional()
  userName?: string;
  
  @IsString()
  action: string;
  
  @IsString()
  details: string;

  constructor(log: Partial<AuditLogEntryDto>) { // Added constructor for consistency if needed
    this.id = log.id;
    this.timestamp = log.timestamp;
    this.userId = log.userId;
    this.userName = log.userName;
    this.action = log.action;
    this.details = log.details;
  }
}
