export interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  imageUrl: string;
  description?: string;
}

export interface Sale {
  id: string;
  productId: string;
  productName?: string;
  customerId: string;
  customerName?: string;
  date: string; // ISO string
  quantity: number;
  pricePerUnit: number;
  totalAmount: number;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other' | 'Prefer not to say';
  locationCity?: string;
}

export interface InventoryItem {
  id: string; 
  productId: string;
  productName: string;
  stockQuantity: number;
  lowStockThreshold: number;
  lastRestockDate: string; // ISO string
}

export interface SalesTrendData {
  date: string;
  sales: number;
}

export interface ProductCategoryDistribution {
  name: string;
  value: number;
}

export interface CustomerDemographicData {
  name: string; 
  value: number;
}

export interface ForecastDataPoint {
  date: string;
  value: number;
  type: 'actual' | 'forecasted';
}

export enum UserRole {
  ADMIN = 'admin',
  SELLER = 'seller',
}

export interface User {
  id: string;
  name: string;
  role: UserRole;
  email?: string; 
}

export interface GroundingChunkWeb {
  uri: string;
  title: string;
}

export interface GroundingChunk {
  web: GroundingChunkWeb;
}

export interface GroundingMetadata {
  groundingChunks?: GroundingChunk[];
}

export interface AISearchResult {
  text: string;
  sources?: GroundingChunk[];
}

export interface AuditLogEntry {
  id: string;
  timestamp: string; 
  userId: string; 
  userName?: string; 
  action: string; 
  details: string; 
}

export interface SystemSettings {
  maintenanceMode: boolean;
  aiServiceEndpoint: string;
  maxRecommendations: number;
}