
import { Module, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuditService } from './audit.service';
import { AuditLog, AuditLogSchema } from './schemas/audit-log.schema';
import { User, UserSchema } from '../auth/schemas/user.schema'; // Import User schema for username lookup

@Global() // Makes AuditService available globally
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AuditLog.name, schema: AuditLogSchema },
      { name: User.name, schema: UserSchema } // Provide UserModel for username lookup in AuditService
    ]),
  ],
  providers: [AuditService],
  exports: [AuditService],
})
export class AuditModule {}
