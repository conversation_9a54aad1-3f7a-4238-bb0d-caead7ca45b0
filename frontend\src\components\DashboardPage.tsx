import React, { useState, useEffect } from 'react';
import SalesTrendChart from './SalesTrendChart';
import TopProductsChart from './TopProductsChart';
import CustomerDemographicsChart from './CustomerDemographicsChart';
import MetricCard from './MetricCard';
import ChartContainer from './common/ChartContainer';
import Spinner from './common/Spinner';
import Alert from './common/Alert';
import { 
    fetchSalesTrends, 
    fetchTopProducts, 
    fetchCustomerDemographics,
    fetchDashboardMetrics
} from '../services/dataService';
import { SalesTrendData, ProductCategoryDistribution, CustomerDemographicData, User } from '../types';

// Icons
const DollarSignIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg>;
const UsersIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" /></svg>;
const ShoppingBagIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" /></svg>;

interface DashboardPageProps {
  currentUser: User | null;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ currentUser }) => {
  const [salesTrends, setSalesTrends] = useState<SalesTrendData[]>([]);
  const [topProducts, setTopProducts] = useState<ProductCategoryDistribution[]>([]);
  const [customerDemographics, setCustomerDemographics] = useState<{ age: CustomerDemographicData[], gender: CustomerDemographicData[] } | null>(null);
  const [totalRevenue, setTotalRevenue] = useState<number>(0);
  const [totalOrders, setTotalOrders] = useState<number>(0);
  const [totalCustomers, setTotalCustomers] = useState<number>(0);
  
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        const [metrics, trends, productsData, demographicsData] = await Promise.all([
          fetchDashboardMetrics(),
          fetchSalesTrends(),
          fetchTopProducts(),
          fetchCustomerDemographics()
        ]);
        
        setTotalRevenue(metrics.totalRevenue);
        setTotalOrders(metrics.totalOrders);
        setTotalCustomers(metrics.totalCustomers);
        setSalesTrends(trends);
        setTopProducts(productsData);
        setCustomerDemographics(demographicsData);

      } catch (err: any) {
        console.error("Failed to load dashboard data:", err);
        setError(err.message || "Failed to load dashboard data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    loadDashboardData();
  }, []);

  if (loading) return <div className="flex justify-center items-center h-screen"><Spinner /></div>;
  if (error) return <div className="p-4"><Alert message={error} type="error" /></div>;

  return (
    <div className="p-4 md:p-6 space-y-6 bg-slate-100 min-h-screen">
      <h2 className="text-3xl font-bold text-gray-800">E-commerce Dashboard</h2>
      {currentUser && <p className="text-gray-600">Welcome, {currentUser.name} ({currentUser.role})!</p>}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <MetricCard title="Total Revenue" value={`$${totalRevenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`} icon={<DollarSignIcon />} />
        <MetricCard title="Total Orders" value={totalOrders.toLocaleString()} icon={<ShoppingBagIcon />} />
        <MetricCard title="Total Customers" value={totalCustomers.toLocaleString()} icon={<UsersIcon />} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer title="Sales Trends">
          <SalesTrendChart data={salesTrends} />
        </ChartContainer>
        <ChartContainer title="Top Selling Products">
          <TopProductsChart data={topProducts} />
        </ChartContainer>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
         {customerDemographics?.age && customerDemographics.age.length > 0 && (
          <ChartContainer title="Customer Demographics (Age)">
            <CustomerDemographicsChart data={customerDemographics.age} title="Age Groups" />
          </ChartContainer>
        )}
         {customerDemographics?.gender && customerDemographics.gender.length > 0 && (
          <ChartContainer title="Customer Demographics (Gender)">
            <CustomerDemographicsChart data={customerDemographics.gender} title="Gender Distribution" />
          </ChartContainer>
        )}
      </div>
    </div>
  );
};

export default DashboardPage;