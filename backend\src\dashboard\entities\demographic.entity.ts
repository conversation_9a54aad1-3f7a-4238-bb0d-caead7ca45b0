import { Entity, Column, PrimaryGeneratedColumn, Check } from 'typeorm';

@Entity('demographics')
export class Demographic {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'customer_id', type: 'varchar' })
  customerId: string;

  @Column({ name: 'age_group', type: 'varchar', nullable: true })
  @Check(`age_group IN ('18-24', '25-34', '35-44', '45-54', '55+') OR age_group IS NULL`)
  ageGroup: string;

  @Column({ type: 'varchar', nullable: true })
  @Check(`gender IN ('Male', 'Female', 'Other') OR gender IS NULL`)
  gender: string;

  @Column({ type: 'varchar', nullable: true })
  location: string;

  @Column({ name: 'acquisition_channel', type: 'varchar', nullable: true })
  acquisitionChannel: string;

  @Column({ name: 'customer_since', type: 'date' })
  customerSince: Date;

  @Column({ name: 'total_orders', type: 'integer', default: 0 })
  totalOrders: number;

  @Column({ name: 'total_spent', type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalSpent: number;
}