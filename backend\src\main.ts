
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger } from '@nestjs/common'; // Import Logger
// import * as dotenv from 'dotenv'; // dotenv.config() will be handled by ConfigModule

async function bootstrap() {
  // dotenv.config(); // ConfigModule will handle loading .env file
  
  const logger = new Logger('Bootstrap');
  logger.log('Starting application...');
  logger.log('Connecting to databases...');
  const mongoDbName = process.env.MONGODB_DATABASE || 'userDatabase';
  const postgresDbName = process.env.POSTGRES_DATABASE || 'postgres';
  logger.log(`MongoDB: Connecting to ${mongoDbName}`);
  logger.log(`PostgreSQL: Connecting to ${postgresDbName}`);

  const app = await NestFactory.create(AppModule);

  // Global prefix for all routes to match frontend's /api
  app.setGlobalPrefix('api');

  // Enable global DTO validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // Strips properties not defined in DTO
    transform: true, // Automatically transforms payloads to DTO instances
  }));

  // Enable CORS (configure appropriately for production)
  app.enableCors({
    origin: '*', // Replace with your frontend URL in production
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type, Accept, Authorization',
  });
  
  const port = process.env.PORT || 3000; // ConfigModule should make PORT available if set in .env
  await app.listen(port);
  logger.log(`Backend application is running on: http://localhost:${port}/api`);
  logger.log('Database connections established:');
  logger.log(`- MongoDB: Connected to ${mongoDbName} (Auth, Admin, Audit)`);
  logger.log(`- PostgreSQL: Connected to ${postgresDbName} (Products, Inventory, Sales, Demographics)`);
}
bootstrap();