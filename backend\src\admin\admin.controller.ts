
import { Controller, Get, Put, Delete, Body, Param, UseGuards, Query, Req, ValidationPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { AdminService } from './admin.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../auth/guards/admin-role.guard';
import { UserAdminResponseDto, UpdateUserRoleDto, SystemSettingsDto, AuditLogEntryDto } from './dto/admin.dto';
import { UserRole } from '../shared/types.shared'; 

@Controller('admin')
@UseGuards(JwtAuthGuard, AdminRoleGuard) // Protect all admin routes
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('users')
  async getAllUsers(): Promise<UserAdminResponseDto[]> {
    return await this.adminService.getAllUsers();
  }

  @Put('users/:userId/role')
  async updateUserRole(
    @Param('userId') userId: string,
    @Body(new ValidationPipe()) updateUserRoleDto: UpdateUserRoleDto,
    @Req() request, // To get current admin user ID
  ): Promise<UserAdminResponseDto> {
    const adminUserId = request.user.sub; // Assuming 'sub' in JWT payload is user ID
    return await this.adminService.updateUserRole(userId, updateUserRoleDto.role, adminUserId);
  }

  @Delete('users/:userId')
  @HttpCode(HttpStatus.NO_CONTENT) // Typically, DELETE returns 204 No Content
  async deleteUser(
      @Param('userId') userId: string,
      @Req() request, // To get current admin user ID
    ): Promise<void> { // Return Promise<void>
    const adminUserId = request.user.sub;
    await this.adminService.deleteUser(userId, adminUserId);
  }

  @Get('settings')
  async getSystemSettings(): Promise<SystemSettingsDto> {
    return await this.adminService.getSystemSettings();
  }

  @Put('settings')
  async updateSystemSettings(
    @Body(new ValidationPipe()) settingsDto: SystemSettingsDto,
    @Req() request,
  ): Promise<SystemSettingsDto> {
    const adminUserId = request.user.sub;
    return await this.adminService.updateSystemSettings(settingsDto, adminUserId);
  }

  @Get('audit-logs')
  async getAuditLogs(@Query('search') searchTerm?: string): Promise<AuditLogEntryDto[]> {
    return await this.adminService.getAuditLogs(searchTerm);
  }
}
