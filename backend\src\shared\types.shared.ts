// These types should ideally mirror or be compatible with your frontend types.ts

export interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  imageUrl: string;
  description?: string;
}

export interface InventoryItem {
  id?: string; 
  productId: string;
  productName: string;
  stockQuantity: number;
  lowStockThreshold: number;
  lastRestockDate: string | Date; // Date in backend, string in DTOs/frontend
}

export enum UserRole {
  ADMIN = 'admin',
  SELLER = 'seller'
}

export interface User {
  id: string;
  name: string;
  email: string;
  password?: string; 
  role: UserRole;
}

export interface JwtPayload {
  sub: string; 
  email: string;
  role: UserRole;
}

export interface AuditLogEntry {
  id: string;
  timestamp: string | Date; 
  userId: string; 
  userName?: string; 
  action: string; 
  details: string; 
}

export interface SalesTrendData {
  date: string; 
  sales: number;
}

export interface ProductCategoryDistribution {
  name: string; 
  value: number;
}

export interface CustomerDemographicData {
  name: string; 
  value: number;
}

export interface GroundingChunkWeb {
  uri: string;
  title: string;
}

export interface GroundingChunk {
  web: GroundingChunkWeb;
}
export interface AISearchResult {
  text: string;
  sources?: GroundingChunk[];
}

export interface ForecastDataPoint {
  date: string;
  value: number;
  type: 'actual' | 'forecasted';
}

export interface SystemSettings {
  maintenanceMode: boolean;
  aiServiceEndpoint: string;
  maxRecommendations: number;
}

// New types for MongoDB Schemas, also used in DTOs/frontend
export interface Sale {
  id?: string;
  productId: string;
  productName: string;
  customerId: string;
  customerName: string;
  date: Date | string; // Date in backend, string in DTOs/frontend
  quantity: number;
  pricePerUnit: number;
  totalAmount: number;
}

export interface Customer {
  id?: string;
  name: string;
  email: string;
  age: number;
  gender: string; 
  locationCity?: string;
}
