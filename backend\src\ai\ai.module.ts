
import { Module } from '@nestjs/common';
import { AiService } from './ai.service';
import { Ai<PERSON>ontroller } from './ai.controller';
// Using require instead of import for @nestjs/config
const { ConfigModule } = require('@nestjs/config');
import { ProductsModule } from '../products/products.module'; // Import ProductsModule

@Module({
  imports: [
    ConfigModule, // Ensure ConfigService is available for API_KEY
    ProductsModule, // Make ProductsService available for injection
  ],
  providers: [AiService],
  controllers: [AiController],
})
export class AiModule {}
