import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

export type InventoryItemDocument = InventoryItem & Document;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class InventoryItem {
  @Prop({ type: String, virtual: true, get() { return this._id?.toHexString(); } })
  id: string;

  @Prop({ type: String, required: true })
  productId: string; 

  @Prop({ required: true, type: String })
  productName: string; 

  @Prop({ required: true, type: Number, default: 0, min: 0 })
  stockQuantity: number;

  @Prop({ required: true, type: Number, default: 10, min: 0 })
  lowStockThreshold: number;

  @Prop({ required: true, type: Date, default: Date.now })
  lastRestockDate: Date;
}

export const InventoryItemSchema = SchemaFactory.createForClass(InventoryItem);
InventoryItemSchema.index({ productId: 1 }, { unique: true }); // Ensure one inventory record per product
