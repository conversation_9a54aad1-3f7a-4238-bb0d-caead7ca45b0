
import { Injectable, UnauthorizedException, InternalServerErrorException, Logger } from '@nestjs/common'; // Import Logger
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from './auth.service';
import { JwtPayload, User } from '../shared/types.shared';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly strategyLogger = new Logger(JwtStrategy.name); // Create a logger instance

  constructor(
    private authService: AuthService,
    private configService: ConfigService,
  ) {
    const secret = configService.get<string>('JWT_SECRET');
    
    // Validate secret and call super() first
    if (!secret) {
      // We can't use this.strategyLogger here before super()
      // Use a static logger instead
      const logger = new Logger(JwtStrategy.name);
      logger.error('JWT_SECRET is not defined in environment variables for JwtStrategy. Please ensure it is set in your .env file in the backend directory.');
      throw new InternalServerErrorException('JWT_SECRET is not defined in environment variables for JwtStrategy. Please ensure it is set in your .env file in the backend directory.');
    }
    
    // Call super() with validated secret
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
    });
  }

  async validate(payload: JwtPayload): Promise<Omit<User, 'password'>> {
    this.strategyLogger.debug(`JWT Strategy validating payload: ${JSON.stringify(payload)}`);
    
    if (!payload || !payload.sub) {
      this.strategyLogger.error('Invalid JWT payload structure');
      throw new UnauthorizedException('Invalid token structure.');
    }
    
    try {
      const userDoc = await this.authService.validateUserPayload(payload);
      if (!userDoc) {
        this.strategyLogger.error(`User validation failed for sub: ${payload.sub}`);
        throw new UnauthorizedException('Invalid token or user does not exist.');
      }
      
      // Convert Mongoose document to a plain object.
      // UserSchema.toObject transform handles virtuals (like id) and removes password.
      const userObject = userDoc.toObject({ virtuals: true });

      // Verify ID is properly set (should be handled by virtual now)
      if (!userObject.id) {
        this.strategyLogger.error('User object missing ID after toObject conversion');
        throw new UnauthorizedException('Invalid user data structure.');
      }
      
      this.strategyLogger.debug(`User validated successfully: ${userObject.email}`);
      return userObject as Omit<User, 'password'>; // The schema's toObject should shape this correctly.
    } catch (error) {
      this.strategyLogger.error(`Error in JWT validation: ${error.message}`);
      throw new UnauthorizedException('Error validating token.');
    }
  }
}
