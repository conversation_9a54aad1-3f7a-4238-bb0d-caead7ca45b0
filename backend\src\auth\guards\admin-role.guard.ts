import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { UserRole, User } from '../../shared/types.shared';

@Injectable()
export class AdminRoleGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user as User; // User object attached by JwtAuthGuard

    if (!user) {
        // This should ideally not happen if JwtAuthGuard is applied before this guard
        throw new ForbiddenException('Authentication required.'); 
    }
    
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Access denied. Admin role required.');
    }
    return true;
  }
}
