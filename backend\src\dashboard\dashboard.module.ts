import { Module } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Sale } from './entities/sale.entity';
import { Demographic } from './entities/demographic.entity';
import { ProductsModule } from '../products/products.module'; // Import ProductsModule

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Sale,
      Demographic,
    ]),
    ProductsModule, // To access ProductsService for product data
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}