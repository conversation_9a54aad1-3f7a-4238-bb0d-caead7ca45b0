
import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import { ConfigService } from '@nestjs/config';
import { Product, ForecastDataPoint, AISearchResult, GroundingChunk } from '../shared/types.shared';
import { SalesForecastRequestDto, ProductRecommendationsRequestDto, AnalyticsInsightsRequestDto } from './dto/ai-request.dto';
import { ProductsService } from '../products/products.service'; // Import ProductsService

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);
  private genAI: GoogleGenAI;

  constructor(
    private configService: ConfigService,
    private productsService: ProductsService, // Inject ProductsService
    ) {
    const apiKey = this.configService.get<string>('API_KEY');
    if (!apiKey) {
      this.logger.warn('Gemini API Key not found. AI features will be disabled or use mock data.');
    }
    this.genAI = new GoogleGenAI({ apiKey });
  }

  private async generateText(prompt: string, modelName: string = 'gemini-2.5-flash-preview-04-17'): Promise<string> {
    if (!this.genAI || !this.configService.get<string>('API_KEY')) { // Check API_KEY again in case it was missing initially
        this.logger.error('Gemini AI client not initialized or API Key missing.');
        throw new InternalServerErrorException('AI service not available.');
    }
    try {
      const response: GenerateContentResponse = await this.genAI.models.generateContent({
        model: modelName,
        contents: prompt,
      });
      return response.text;
    } catch (error) {
      this.logger.error(`Error calling Gemini API with model ${modelName}: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to generate AI content.');
    }
  }
  
  private parseJsonFromText<T>(text: string, context: string): T | null {
    let jsonStr = text.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    try {
      return JSON.parse(jsonStr) as T;
    } catch (e) {
      this.logger.error(`Failed to parse JSON response for ${context}: ${e.message}. Raw text was: "${text}"`);
      return null;
    }
  }

  async getSalesForecast(dto: SalesForecastRequestDto): Promise<ForecastDataPoint[]> {
    this.logger.log(`Generating sales forecast for product ID: ${dto.productId}`);
    const historicalSalesString = dto.historicalSales.map(s => `${s.date}: ${s.sales} units`).join(', ');
    
    const prompt = `
      Given the following historical sales data for product "${dto.productName}" (ID: ${dto.productId}):
      ${historicalSalesString}.
      The product description is: "${dto.productDescription || 'Not available'}".
      
      Please provide a sales forecast for the next 6 months.
      Return the forecast as a JSON array of objects, where each object has "date" (YYYY-MM-DD) and "value" (predicted sales units).
      For example: [{"date": "2024-01-01", "value": 30}, {"date": "2024-02-01", "value": 32}]
      The forecast should be realistic based on the provided data. Only provide the JSON array.
    `;

    try {
      const responseText = await this.generateText(prompt);
      const parsedForecast = this.parseJsonFromText<Array<{date: string; value: number}>>(responseText, 'sales forecast');

      if (!parsedForecast || !Array.isArray(parsedForecast)) {
         this.logger.warn(`Could not parse sales forecast for ${dto.productId}. Returning mock/empty forecast.`);
         return this.getMockForecastData(dto.historicalSales.length > 0 ? dto.historicalSales[dto.historicalSales.length -1].date : new Date().toISOString().split('T')[0]);
      }
      
      return parsedForecast.map(f => ({ ...f, type: 'forecasted' as 'forecasted' }));

    } catch (error) {
      this.logger.error(`Error in getSalesForecast for product ${dto.productId}: ${error.message}`);
      return this.getMockForecastData(dto.historicalSales.length > 0 ? dto.historicalSales[dto.historicalSales.length -1].date : new Date().toISOString().split('T')[0]);
    }
  }
  
  private getMockForecastData(lastActualDateStr: string): ForecastDataPoint[] {
    const forecast: ForecastDataPoint[] = [];
    let lastDate = new Date(lastActualDateStr + "T00:00:00Z");
    for (let i = 1; i <= 6; i++) {
        lastDate.setUTCMonth(lastDate.getUTCMonth() + 1);
        forecast.push({
            date: lastDate.toISOString().split('T')[0],
            value: Math.floor(Math.random() * 20) + 10, 
            type: 'forecasted',
        });
    }
    this.logger.log('Returning mock forecast data due to an issue or lack of AI response.');
    return forecast;
  }

  async getProductRecommendations(dto: ProductRecommendationsRequestDto): Promise<Product[]> {
    this.logger.log(`Getting recommendations for product ID: ${dto.productId}, type: ${dto.recommendationType}`);
    
    const productContext = `
      Product Name: ${dto.productName}
      Category: ${dto.category}
      Description: ${dto.productDescription || 'N/A'}
    `;

    const recommendationPrompt = `
      Based on the product:
      ${productContext}

      Imagine a product catalog. Suggest 3 product IDs from such a catalog (e.g., "prod_002", "prod_123", "prod_abc") that are typically "${dto.recommendationType === 'frequently_bought_together' ? 'frequently bought together' : 'also viewed by customers'}".
      Do not use the current product ID: ${dto.productId}.
      Return the result ONLY as a JSON array of these product ID strings. Example: ["prod_002", "prod_004", "prod_005"].
    `;

    try {
      const responseText = await this.generateText(recommendationPrompt);
      const recommendedProductIds = this.parseJsonFromText<string[]>(responseText, 'product recommendations');

      if (!recommendedProductIds || !Array.isArray(recommendedProductIds)) {
        this.logger.warn(`Could not parse product recommendations for ${dto.productId}. Returning mock recommendations from DB.`);
        return this.getDbMockRecommendations(dto.productId);
      }

      // Fetch actual product details from the database using ProductsService
      const products: Product[] = [];
      for (const id of recommendedProductIds) {
        if (id === dto.productId) continue; // Skip the current product
        try {
          const product = await this.productsService.findOne(id); // Assumes product IDs match
          if (product) products.push(product);
        } catch (e) {
          // If a recommended product ID is not found in DB, log it but continue
          this.logger.warn(`Recommended product ID ${id} not found in database.`);
        }
        if (products.length >= 3) break; // Limit to 3
      }
      
      if (products.length === 0 && recommendedProductIds.length > 0) {
        this.logger.warn(`AI recommended IDs, but none found in DB. Falling back to DB mock recommendations.`);
        return this.getDbMockRecommendations(dto.productId);
      }
      return products;

    } catch (error) {
      this.logger.error(`Error in getProductRecommendations for product ${dto.productId}: ${error.message}`);
      return this.getDbMockRecommendations(dto.productId); // Fallback to DB mock
    }
  }

  private async getDbMockRecommendations(currentProductId: string): Promise<Product[]> {
    // Fetches a few other products from the DB as a fallback
    try {
      const allProducts = await this.productsService.findAll();
      return allProducts
        .filter(p => p.id !== currentProductId)
        .slice(0, 2); // Simple mock: return first 2 other products from DB
    } catch (dbError) {
      this.logger.error(`Failed to fetch products for mock recommendations: ${dbError.message}`);
      return [];
    }
  }

  async searchAnalyticsInsights(dto: AnalyticsInsightsRequestDto): Promise<AISearchResult> {
    this.logger.log(`Searching analytics insights for query: "${dto.query}"`);
    const modelName = 'gemini-2.5-flash-preview-04-17';
    
    if (!this.genAI || !this.configService.get<string>('API_KEY')) {
        this.logger.error('Gemini AI client not initialized for insights search.');
        throw new InternalServerErrorException('AI service not available.');
    }

    try {
      const response: GenerateContentResponse = await this.genAI.models.generateContent({
        model: modelName,
        contents: `Considering general e-commerce analytics principles and potentially using your knowledge, answer the following question: "${dto.query}"`,
        config: {
          tools: [{ googleSearch: {} }],
        },
      });
      
      const text = response.text;
      let sources: GroundingChunk[] = [];

      if (response.candidates && response.candidates[0]?.groundingMetadata?.groundingChunks) {
         sources = response.candidates[0].groundingMetadata.groundingChunks
           .filter(chunk => chunk.web && chunk.web.uri) 
           .map(chunk => ({
             web: {
               uri: chunk.web.uri,
               title: chunk.web.title || chunk.web.uri, 
             }
           }));
      }
      
      return { text, sources };

    } catch (error) {
      this.logger.error(`Error calling Gemini API for insights search: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to generate AI insights.');
    }
  }
}
