

import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog, AuditLogDocument } from './schemas/audit-log.schema';
import { User, UserDocument } from '../auth/schemas/user.schema'; // For fetching userName

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLogDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>, // Optional: to fetch userName
  ) {}

  async create(
    userId: string,
    action: string,
    details: string,
    userNameAttempt?: string, // userName can be passed if already known
  ): Promise<AuditLog> {
    let resolvedUserName = userNameAttempt;
    if (!resolvedUserName && userId !== 'system-event') { // Don't lookup for system events
        try {
            const user = await this.userModel.findById(userId).select('name').exec();
            if (user) {
                resolvedUserName = user.name;
            }
        } catch (error) {
            this.logger.warn(`Could not fetch username for audit log for userId: ${userId}`);
        }
    }

    const newLog = new this.auditLogModel({
      userId,
      userName: resolvedUserName || (userId === 'system-event' ? 'System' : undefined),
      action,
      details,
    });
    try {
      return await newLog.save();
    } catch (error) {
      this.logger.error(`Failed to save audit log: ${error.message}`, error.stack);
      // Depending on policy, you might want to throw or just log
      throw new InternalServerErrorException('Failed to record audit log.');
    }
  }

  async findAll(limit: number = 50, searchTerm?: string): Promise<AuditLogDocument[]> {
    const queryOptions: any = {};
    if (searchTerm) {
        const regex = new RegExp(searchTerm, 'i');
        queryOptions.$or = [
            { action: regex },
            { details: regex },
            { userName: regex },
            { userId: regex },
        ];
    }
    return this.auditLogModel.find(queryOptions).sort({ timestamp: -1 }).limit(limit).exec();
  }
}