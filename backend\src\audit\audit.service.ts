

import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog, AuditLogDocument } from './schemas/audit-log.schema';
import { User, UserDocument } from '../auth/schemas/user.schema'; // For fetching userName

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLogDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>, // Optional: to fetch userName
  ) {}

  async create(
    userId: string,
    action: string,
    details: string,
    userNameAttempt?: string, // userName can be passed if already known
  ): Promise<AuditLog> {
    // Validate required parameters
    if (!userId) {
      this.logger.error('Audit log creation failed: userId is required');
      throw new InternalServerErrorException('Failed to record audit log: User ID is required.');
    }
    if (!action) {
      this.logger.error('Audit log creation failed: action is required');
      throw new InternalServerErrorException('Failed to record audit log: Action is required.');
    }
    if (!details) {
      this.logger.error('Audit log creation failed: details is required');
      throw new InternalServerErrorException('Failed to record audit log: Details are required.');
    }

    this.logger.debug(`Creating audit log: userId=${userId}, action=${action}`);

    let resolvedUserName = userNameAttempt;
    if (!resolvedUserName && userId !== 'system-event') { // Don't lookup for system events
        try {
            const user = await this.userModel.findById(userId).select('name').exec();
            if (user) {
                resolvedUserName = user.name;
            }
        } catch (error) {
            this.logger.warn(`Could not fetch username for audit log for userId: ${userId}: ${error.message}`);
        }
    }

    const auditLogData = {
      userId,
      userName: resolvedUserName || (userId === 'system-event' ? 'System' : undefined),
      action,
      details,
    };

    this.logger.debug(`Audit log data: ${JSON.stringify(auditLogData)}`);

    const newLog = new this.auditLogModel(auditLogData);
    try {
      const savedLog = await newLog.save();
      this.logger.debug(`Audit log saved successfully with ID: ${savedLog.id}`);
      return savedLog;
    } catch (error) {
      this.logger.error(`Failed to save audit log: ${error.message}`, error.stack);
      this.logger.error(`Audit log data that failed: ${JSON.stringify(auditLogData)}`);
      // Depending on policy, you might want to throw or just log
      throw new InternalServerErrorException('Failed to record audit log.');
    }
  }

  async findAll(limit: number = 50, searchTerm?: string): Promise<AuditLogDocument[]> {
    const queryOptions: any = {};
    if (searchTerm) {
        const regex = new RegExp(searchTerm, 'i');
        queryOptions.$or = [
            { action: regex },
            { details: regex },
            { userName: regex },
            { userId: regex },
        ];
    }
    return this.auditLogModel.find(queryOptions).sort({ timestamp: -1 }).limit(limit).exec();
  }
}