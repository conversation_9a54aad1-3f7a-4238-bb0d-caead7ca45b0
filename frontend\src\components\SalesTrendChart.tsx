import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import { SalesTrendData } from '../types';
import { CHART_COLORS } from '../constants';

interface SalesTrendChartProps {
  data: SalesTrendData[];
}

const SalesTrendChart: React.FC<SalesTrendChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <p className="text-center text-gray-500">No sales data available to display trends.</p>;
  }
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
        <XAxis dataKey="date" tick={{ fontSize: 12 }} />
        <YAxis tick={{ fontSize: 12 }} tickFormatter={(value) => `$${value.toLocaleString()}`} />
        <Tooltip formatter={(value: number) => [`$${value.toLocaleString()}`, "Sales"]}/>
        <Legend wrapperStyle={{ fontSize: "14px" }} />
        <Line type="monotone" dataKey="sales" stroke={CHART_COLORS[0]} strokeWidth={2} activeDot={{ r: 6 }} dot={{r:4}} />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default SalesTrendChart;