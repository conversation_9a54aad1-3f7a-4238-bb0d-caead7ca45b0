import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CustomerDocument = Customer & Document;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class Customer {
  @Prop({ type: String, virtual: true, get() { return this._id?.toHexString(); } })
  id: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ required: true, unique: true, type: String, index: true })
  email: string;

  @Prop({ type: Number, min: 1, max: 120 })
  age: number;

  @Prop({ type: String, enum: ['Male', 'Female', 'Other', 'Prefer not to say'] })
  gender: string;
  
  @Prop({ type: String })
  locationCity?: string;
}

export const CustomerSchema = SchemaFactory.createForClass(Customer);
