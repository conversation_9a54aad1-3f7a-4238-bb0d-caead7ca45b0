// Base URL for your API. Adjust if your API is hosted elsewhere.
const API_BASE_URL = 'http://localhost:3000/api'; 

interface ApiErrorData {
  message?: string | string[]; // NestJS validation pipe can return array of messages
  error?: string;
  statusCode?: number;
}

export class ApiError extends Error {
  public statusCode: number;
  public errorData: ApiErrorData;

  constructor(message: string, statusCode: number, errorData: ApiErrorData = {}) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.errorData = errorData;
  }
}

function formatErrorMessage(errorDetails: ApiErrorData): string {
    if (Array.isArray(errorDetails.message)) {
        return errorDetails.message.join(', ');
    }
    return errorDetails.message || `Request failed with status ${errorDetails.statusCode}`;
}

async function handleResponse<T>(response: Response): Promise<T> {
  const contentType = response.headers.get('content-type');
  let responseData;

  if (contentType && contentType.includes('application/json')) {
    responseData = await response.json();
  } else {
    // If not JSON, and not OK, read text for error. If OK, return empty for void responses.
    if (response.ok && response.status !== 204) { // 204 No Content should return undefined/void
         // If server returns 200 OK with non-JSON, it's unexpected for this app's API design
         // but we'll try to handle it gracefully.
         console.warn("Received non-JSON OK response from API", response);
         return {} as T; // Or handle as error if non-JSON OK is not expected
    } else if (response.ok && response.status === 204) {
        return undefined as T; // For DELETE or other no-content success responses
    }
    // If not OK, try to get a text message
    const textError = await response.text();
    responseData = { message: textError || `HTTP error! status: ${response.status}` }; 
  }
  
  if (!response.ok) {
    const errorDetails: ApiErrorData = responseData; // responseData will have message from above
    throw new ApiError(
      formatErrorMessage(errorDetails),
      response.status,
      errorDetails
    );
  }
  return responseData as T;
}

export async function get<T>(endpoint: string, token?: string): Promise<T> {
  const headers: HeadersInit = { 'Content-Type': 'application/json' };
  if (token) headers['Authorization'] = `Bearer ${token}`;

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    method: 'GET',
    headers,
  });
  return handleResponse<T>(response);
}

export async function post<T, R>(endpoint: string, data: T, token?: string): Promise<R> {
  const headers: HeadersInit = { 'Content-Type': 'application/json' };
  if (token) headers['Authorization'] = `Bearer ${token}`;
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    method: 'POST',
    headers,
    body: JSON.stringify(data),
  });
  return handleResponse<R>(response);
}

export async function put<T, R>(endpoint: string, data: T, token?: string): Promise<R> {
  const headers: HeadersInit = { 'Content-Type': 'application/json' };
  if (token) headers['Authorization'] = `Bearer ${token}`;

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    method: 'PUT',
    headers,
    body: JSON.stringify(data),
  });
  return handleResponse<R>(response);
}

export async function del<R>(endpoint: string, token?: string): Promise<R> {
  const headers: HeadersInit = { 'Content-Type': 'application/json' };
  if (token) headers['Authorization'] = `Bearer ${token}`;
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    method: 'DELETE',
    headers,
  });
  return handleResponse<R>(response); // R could be void for 204 responses
}