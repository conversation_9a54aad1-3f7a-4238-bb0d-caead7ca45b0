import React, { useState, useEffect, useCallback } from 'react';
import { User, UserRole, AuditLogEntry, SystemSettings } from '../types';
import { 
    fetchAdminSystemUsers, 
    updateAdminUserRole, 
    deleteAdminUser,
    fetchAdminSystemSettings,
    updateAdminSystemSettings,
    fetchAdminAuditLogs
} from '../services/dataService';
import Alert from './common/Alert';
import Spinner from './common/Spinner';

interface AdminPanelPageProps {
  currentUser: User | null;
}

const AdminPanelPage: React.FC<AdminPanelPageProps> = ({ currentUser }) => {
  const [systemUsers, setSystemUsers] = useState<User[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [filteredAuditLogs, setFilteredAuditLogs] = useState<AuditLogEntry[]>([]);
  const [auditLogSearchTerm, setAuditLogSearchTerm] = useState<string>('');

  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    maintenanceMode: false,
    aiServiceEndpoint: '',
    maxRecommendations: 0,
  });
  const [tempSettings, setTempSettings] = useState<SystemSettings>(systemSettings);
  
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [loadingLogs, setLoadingLogs] = useState(true);
  const [actionInProgress, setActionInProgress] = useState(false);

  const [message, setMessage] = useState<{text: string, type: 'success' | 'error' | 'info'} | null>(null);

  const clearMessage = useCallback(() => setTimeout(() => setMessage(null), 4000), []);

  const loadInitialData = useCallback(async () => {
    try {
      setLoadingUsers(true);
      setLoadingSettings(true);
      setLoadingLogs(true);
      setMessage(null);

      const [users, settings, logs] = await Promise.all([
        fetchAdminSystemUsers(),
        fetchAdminSystemSettings(),
        fetchAdminAuditLogs()
      ]);

      setSystemUsers(users);
      setSystemSettings(settings);
      setTempSettings(settings); 
      setAuditLogs(logs);
      setFilteredAuditLogs(logs);

    } catch (err: any) {
      setMessage({ text: err.message || "Failed to load admin data.", type: 'error' });
      clearMessage();
    } finally {
      setLoadingUsers(false);
      setLoadingSettings(false);
      setLoadingLogs(false);
    }
  }, [clearMessage]);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);


  useEffect(() => {
    const filtered = auditLogSearchTerm ? auditLogs.filter(log =>
      log.action.toLowerCase().includes(auditLogSearchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(auditLogSearchTerm.toLowerCase()) ||
      log.userName?.toLowerCase().includes(auditLogSearchTerm.toLowerCase()) ||
      log.userId.toLowerCase().includes(auditLogSearchTerm.toLowerCase())
    ) : auditLogs;
    setFilteredAuditLogs(filtered);
  }, [auditLogSearchTerm, auditLogs]);

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    if (userId === currentUser?.id && newRole !== UserRole.ADMIN) {
      setMessage({ text: "Cannot remove admin role from yourself.", type: 'error' });
      clearMessage();
      return;
    }
    setActionInProgress(true);
    try {
      const updatedUser = await updateAdminUserRole(userId, newRole);
      setSystemUsers(prevUsers =>
        prevUsers.map(user => (user.id === updatedUser.id ? updatedUser : user))
      );
      setMessage({ text: `Role for ${updatedUser.name} changed to ${newRole}.`, type: 'success' });
      loadInitialData(); // Refresh logs
    } catch (err: any) {
      setMessage({ text: err.message || "Failed to update user role.", type: 'error' });
    } finally {
      setActionInProgress(false);
      clearMessage();
    }
  };

  const handleDeleteUser = async (userId: string, userName: string) => {
    if (userId === currentUser?.id) {
      setMessage({ text: "Cannot delete yourself.", type: 'error' });
      clearMessage();
      return;
    }
    if (!window.confirm(`Are you sure you want to delete user ${userName}? This action cannot be undone.`)) {
        return;
    }
    setActionInProgress(true);
    try {
      await deleteAdminUser(userId);
      setSystemUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
      setMessage({ text: `User ${userName} deleted successfully.`, type: 'success' });
      loadInitialData(); // Refresh logs
    } catch (err: any) {
      setMessage({ text: err.message || "Failed to delete user.", type: 'error' });
    } finally {
      setActionInProgress(false);
      clearMessage();
    }
  };

  const handleSettingChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
        const { checked } = e.target as HTMLInputElement;
        setTempSettings(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
        setTempSettings(prev => ({ ...prev, [name]: parseInt(value, 10) }));
    } else {
        setTempSettings(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSaveSettings = async () => {
    setActionInProgress(true);
    try {
      const updatedSettings = await updateAdminSystemSettings(tempSettings);
      setSystemSettings(updatedSettings);
      setTempSettings(updatedSettings); 
      setMessage({ text: "System settings saved successfully.", type: 'success' });
      loadInitialData(); // Refresh logs
    } catch (err: any) {
      setMessage({ text: err.message || "Failed to save system settings.", type: 'error' });
    } finally {
      setActionInProgress(false);
      clearMessage();
    }
  };
  
  const isLoadingInitialData = loadingUsers || loadingSettings || loadingLogs;

  return (
    <div className="p-4 md:p-6 space-y-6 bg-slate-100 min-h-screen">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">Admin Panel</h2>
      </div>
      
      {message && <Alert type={message.type} message={message.text} onClose={() => setMessage(null)} />}

      {isLoadingInitialData && <div className="flex justify-center items-center h-64"><Spinner /></div>}

      {!isLoadingInitialData && (
        <>
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h3 className="text-xl font-semibold text-gray-700 mb-4">User Management</h3>
            {loadingUsers ? <Spinner /> : (
            <div className="overflow-x-auto mt-2">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {systemUsers.map(user => (
                    <tr key={user.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <select 
                          value={user.role} 
                          onChange={(e) => handleRoleChange(user.id, e.target.value as UserRole)}
                          disabled={actionInProgress || (user.id === currentUser?.id && user.role === UserRole.ADMIN)}
                          className="p-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                        >
                          <option value={UserRole.ADMIN}>Admin</option>
                          <option value={UserRole.SELLER}>Seller</option>
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button 
                          onClick={() => handleDeleteUser(user.id, user.name)}
                          disabled={actionInProgress || user.id === currentUser?.id}
                          className="text-red-600 hover:text-red-800 disabled:text-gray-400 disabled:cursor-not-allowed"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            )}
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h3 className="text-xl font-semibold text-gray-700 mb-4">System Settings</h3>
            {loadingSettings ? <Spinner/> : (
            <div className="space-y-4 mt-2">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="maintenanceMode" 
                  name="maintenanceMode"
                  checked={tempSettings.maintenanceMode}
                  onChange={handleSettingChange}
                  disabled={actionInProgress}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:bg-gray-100" 
                />
                <label htmlFor="maintenanceMode" className="ml-2 block text-sm text-gray-900">
                  Enable Maintenance Mode
                </label>
              </div>
              <div>
                <label htmlFor="aiServiceEndpoint" className="block text-sm font-medium text-gray-700">
                  AI Service Endpoint
                </label>
                <input 
                  type="text" 
                  id="aiServiceEndpoint" 
                  name="aiServiceEndpoint"
                  value={tempSettings.aiServiceEndpoint}
                  onChange={handleSettingChange}
                  disabled={actionInProgress}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100"
                />
              </div>
              <div>
                <label htmlFor="maxRecommendations" className="block text-sm font-medium text-gray-700">
                  Max Recommendations Displayed
                </label>
                <input 
                  type="number" 
                  id="maxRecommendations" 
                  name="maxRecommendations"
                  value={tempSettings.maxRecommendations}
                  onChange={handleSettingChange}
                  min="1"
                  max="10"
                  disabled={actionInProgress}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100"
                />
              </div>
              <button 
                onClick={handleSaveSettings}
                disabled={actionInProgress}
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:bg-gray-300"
              >
                {actionInProgress ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
            )}
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h3 className="text-xl font-semibold text-gray-700 mb-4">Audit Logs</h3>
            <input 
              type="text"
              placeholder="Search logs by action, details, or user..."
              value={auditLogSearchTerm}
              onChange={(e) => setAuditLogSearchTerm(e.target.value)}
              className="mb-4 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            {loadingLogs ? <Spinner/> : (
            <div className="overflow-x-auto max-h-96">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAuditLogs.length > 0 ? filteredAuditLogs.map(log => (
                    <tr key={log.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(log.timestamp).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.userName || log.userId}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.action}</td>
                      <td className="px-6 py-4 whitespace-normal text-sm text-gray-500 max-w-md break-words">{log.details}</td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500">No audit logs found matching your search.</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default AdminPanelPage;