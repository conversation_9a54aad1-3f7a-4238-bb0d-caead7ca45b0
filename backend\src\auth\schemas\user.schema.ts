
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { UserRole } from '../../shared/types.shared'; // Ensure this path is correct

export type UserDocument = User & Document & {
  // Explicitly define methods here if not part of the class for better type inference with Mongoose
  // comparePassword: (password: string) => Promise<boolean>; 
};

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      delete ret.password; // Ensure password is not returned by default
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    },
  },
})
export class User {
  // Virtual id field - will be defined on the schema below
  id: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ required: true, unique: true, type: String })
  email: string;

  @Prop({ required: true, type: String, select: false }) // Keep select: false if you usually want to hide it
  password?: string; 

  @Prop({ required: true, enum: UserRole, default: UserRole.SELLER, type: String })
  role: UserRole;

  // Method signature for type safety, implementation is via Schema.methods
  async comparePassword(candidatePassword: string): Promise<boolean> {
    // This body is for type definition; actual logic is in UserSchema.methods.comparePassword
    // but by having it here, `this.password` can be type-checked if it were used.
    // The actual implementation will be used by Mongoose.
    return bcrypt.compare(candidatePassword, this.password || "");
  }
}

export const UserSchema = SchemaFactory.createForClass(User);

// Add virtual id field
UserSchema.virtual('id').get(function() {
  return this._id ? this._id.toHexString() : undefined;
});

// Middleware to hash password before saving
UserSchema.pre<UserDocument>('save', async function (next) {
  // Check if 'this' is a Mongoose Document before accessing 'isModified'
  if (this.isModified && this.isModified('password') && this.password) {
    const salt = await bcrypt.genSalt();
    this.password = await bcrypt.hash(this.password, salt);
  }
  next();
});

// Method to compare password
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  if (!this.password) return false; // Should not happen if password field is selected
  return bcrypt.compare(candidatePassword, this.password);
};
