
import { Modu<PERSON>, InternalServerErrorException, Logger } from '@nestjs/common'; // Added Logger
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './jwt.strategy';
import { jwtConstants } from './constants';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from './schemas/user.schema';
import { ConfigModule, ConfigService } from '@nestjs/config';

const authModuleLogger = new Logger('AuthModule'); // Create a logger instance for the module

@Module({
  imports: [
    ConfigModule, 
    PassportModule,
    JwtModule.registerAsync({ 
      imports: [ConfigModule], 
      useFactory: async (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_SECRET');
        

        
        if (!secret) {
          authModuleLogger.error('JWT_SECRET is not defined in environment variables for JwtModule. Please ensure it is set in your .env file in the backend directory.');
          throw new InternalServerErrorException('JWT_SECRET is not defined in environment variables for JwtModule. Please ensure it is set in your .env file in the backend directory.');
        }
        return {
          secret: secret,
          signOptions: { expiresIn: jwtConstants.expiresIn },
        };
      },
      inject: [ConfigService], 
    }),
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
  ],
  providers: [AuthService, JwtStrategy],
  controllers: [AuthController],
  exports: [AuthService], 
})
export class AuthModule {}
