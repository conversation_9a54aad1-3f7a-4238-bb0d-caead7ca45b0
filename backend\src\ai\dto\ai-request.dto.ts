import { IsString, <PERSON>NotEmpty, IsOptional, ValidateNested, IsArray, ArrayMinSize, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

class HistoricalSaleItemDto {
  @IsString()
  @IsNotEmpty()
  date: string;

  @IsNotEmpty()
  sales: number;
}

export class SalesForecastRequestDto {
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsString()
  @IsNotEmpty()
  productName: string;

  @IsString()
  @IsOptional()
  productDescription?: string;
  
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => HistoricalSaleItemDto)
  historicalSales: HistoricalSaleItemDto[];
}

export enum RecommendationType {
    FREQUENTLY_BOUGHT_TOGETHER = 'frequently_bought_together',
    CUSTOMERS_ALSO_VIEWED = 'customers_also_viewed',
}

export class ProductRecommendationsRequestDto {
  @IsString()
  @IsNotEmpty()
  productId: string;
  
  @IsString()
  @IsNotEmpty()
  productName: string;

  @IsString()
  @IsOptional()
  productDescription?: string;

  @IsString()
  @IsNotEmpty()
  category: string;

  @IsEnum(RecommendationType)
  @IsNotEmpty()
  recommendationType: RecommendationType;
}

export class AnalyticsInsightsRequestDto {
  @IsString()
  @IsNotEmpty()
  query: string;
}
