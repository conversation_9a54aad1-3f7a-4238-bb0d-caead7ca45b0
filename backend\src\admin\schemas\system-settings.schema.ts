
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type SystemSettingsDocument = SystemSettings & Document;

// This schema is intended for a collection that will likely hold only one document
@Schema({ 
    timestamps: true,
    toJSON: {
        virtuals: true, // Ensure virtuals are included if you add any
        transform: (doc, ret) => {
          delete ret._id;
          delete ret.__v;
          return ret;
        },
    },
    toObject: {
        virtuals: true,
        transform: (doc, ret) => {
          delete ret._id;
          delete ret.__v;
          return ret;
        },
    }
})
export class SystemSettings {
  // No explicit 'id' virtual needed if not referenced directly often,
  // as it's a single-document collection.
  // If you prefer an id for consistency:
  // @Prop({ type: String, virtual: true, get() { return this._id?.toHexString(); } })
  // id: string;

  @Prop({ required: true, type: Boolean, default: false })
  maintenanceMode: boolean;

  @Prop({ required: true, type: String, default: '/gemini/api/v1/generate' })
  aiServiceEndpoint: string;

  @Prop({ required: true, type: Number, default: 5 })
  maxRecommendations: number;

  // Add other system-wide settings here
}

export const SystemSettingsSchema = SchemaFactory.createForClass(SystemSettings);
