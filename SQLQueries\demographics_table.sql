CREATE TABLE demographics (
    id SERIAL PRIMARY KEY,
    customer_id VARCHAR NOT NULL,
    age_group TEXT,
    gender TEXT,
    location TEXT,
    acquisition_channel TEXT,
    customer_since DATE,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    CHECK (age_group IN ('18-24', '25-34', '35-44', '45-54', '55+') OR age_group IS NULL),
    CHECK (gender IN ('Male', 'Female', 'Other') OR gender IS NULL)
);