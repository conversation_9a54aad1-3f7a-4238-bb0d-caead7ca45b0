
import React, { useState, useEffect } from 'react';
import { HashRouter, Routes, Route, Link, useLocation, Navigate } from 'react-router-dom';
import DashboardPage from './components/DashboardPage';
import InventoryPage from './components/InventoryPage';
import ForecastingPage from './components/ForecastingPage';
import RecommendationsPage from './components/RecommendationsPage';
import LoginPage from './components/LoginPage';
import AdminPanelPage from './components/AdminPanelPage';
import { User, UserRole } from './types';
import { APP_NAME } from './constants';
import { LoginResponse, fetchCurrentUserSession, logoutUser as apiLogoutUser } from './services/authService';
import Spinner from './components/common/Spinner';


// Simple Icons for Nav (SVGs remain the same)
const HomeIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5"><path strokeLinecap="round" strokeLinejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h7.5" /></svg>;
const InventoryIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5"><path strokeLinecap="round" strokeLinejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10.5 11.25h3M12 15V7.5m-2.25 1.5h4.5" /></svg>;
const ForecastIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" /></svg>;
const RecommendIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5"><path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.25 12h.008v.008h-.008V12Zm0 0h.008v.008h-.008V12Zm0 0h.008v.008h-.008V12Zm0 0h.008v.008h-.008V12Z" /></svg>;
const UserIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5"><path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" /></svg>;
const LogoutIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 mr-2"><path strokeLinecap="round" strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9" /></svg>;
const AdminIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5"><path strokeLinecap="round" strokeLinejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774a1.125 1.125 0 0 1 .12 1.45l-.527.737c-.25.35-.272.806-.108 1.204.165.397.505.71.93.78l.893.15c.543.09.94.56.94 1.11v1.093c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.383-.93.78-.165.398-.143.854.107 1.204l.527.738c.316.44.243 1.035-.12 1.45l-.773.773a1.125 1.125 0 0 1-1.45.12l-.737-.527c-.35-.25-.806-.272-1.203-.107-.397.165-.71.505-.78.93l-.15.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.149-.894c-.07-.424-.384-.764-.78-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.44.316-1.035.243-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.273-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.11v-1.094c0 .55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.93l.15-.893Z" /><path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" /></svg>;


interface NavLinkProps {
  to: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  isSidebarOpen: boolean;
}

const NavLinkItem: React.FC<NavLinkProps> = ({ to, children, icon, isSidebarOpen }) => {
  const location = useLocation();
  const isActive = location.pathname === to;
  return (
    <Link
      to={to}
      title={isSidebarOpen ? "" : String(children)}
      className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors duration-150 ease-in-out
                  ${isSidebarOpen ? 'justify-start' : 'justify-center'}
                  ${isActive ? 'bg-blue-600 text-white shadow-md' : 'text-gray-100 hover:bg-blue-500 hover:text-white'}`}
    >
      {icon && <span className={isSidebarOpen ? "mr-2" : ""}>{icon}</span>}
      {isSidebarOpen && children}
    </Link>
  );
};

interface ProtectedRouteProps {
  currentUser: User | null;
  isLoading: boolean;
  children: React.ReactNode; // Changed from JSX.Element
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ currentUser, isLoading, children }) => {
  if (isLoading) {
    return <div className="flex justify-center items-center h-screen"><Spinner /></div>;
  }
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }
  return <>{children}</>; // Use React Fragment or directly return children if it's a single element
};

const App: React.FC = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [authToken, setAuthToken] = useState<string | null>(localStorage.getItem('authToken'));
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const attemptSessionRestore = async () => {
      const token = localStorage.getItem('authToken');
      if (token) {
        try {
          const user = await fetchCurrentUserSession(token);
          if (user) {
            // Ensure the role is correctly set to match the backend enum values
            if (user.role) {
              // Force the role to be lowercase to match backend enum
              if (user.role === 'Admin') {
                user.role = 'admin';
              } else if (user.role === 'Seller') {
                user.role = 'seller';
              }
            }
            
            console.log('Session restored, user data:', user);
            
            setCurrentUser(user);
            setAuthToken(token);
          } else {
            console.log('Session restore failed: No user returned');
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            setAuthToken(null);
          }
        } catch (error) {
          console.error("Session restore failed:", error);
          localStorage.removeItem('authToken');
          localStorage.removeItem('currentUser');
          setAuthToken(null);
        }
      }
      setIsLoading(false);
    };
    attemptSessionRestore();
  }, []);

  const handleLoginSuccess = (data: LoginResponse) => {
    // Ensure the role is correctly set to match the backend enum values
    if (data.user && data.user.role) {
      // Force the role to be lowercase to match backend enum
      if (data.user.role === 'Admin') {
        data.user.role = 'admin';
      } else if (data.user.role === 'Seller') {
        data.user.role = 'seller';
      }
    }
    
    console.log('Login successful, user data:', data.user);
    
    setCurrentUser(data.user);
    setAuthToken(data.token);
    localStorage.setItem('authToken', data.token);
    localStorage.setItem('currentUser', JSON.stringify(data.user));
  };

  const handleLogout = async () => {
    const currentToken = authToken;
    setCurrentUser(null);
    setAuthToken(null);
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    if (currentToken) {
      try {
        await apiLogoutUser(currentToken);
      } catch (error) {
        console.error("Error during API logout:", error);
      }
    }
  };

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  if (isLoading && !currentUser) {
    return <div className="flex justify-center items-center h-screen"><Spinner /></div>;
  }

  return (
    <HashRouter>
      <Routes>
        <Route
            path="/login"
            element={
                !isLoading && currentUser ? <Navigate to="/" /> : <LoginPage onLoginSuccess={handleLoginSuccess} />
            }
        />
        <Route
          path="/*"
          element={
            <ProtectedRoute currentUser={currentUser} isLoading={isLoading}>
              <div className="flex h-screen bg-gray-100">
                <aside className={`bg-blue-700 text-white ${isSidebarOpen ? 'w-64' : 'w-20'} flex-shrink-0 transition-all duration-300 ease-in-out flex flex-col shadow-lg`}>
                  <div className={`flex items-center ${isSidebarOpen ? 'justify-between p-4' : 'justify-center py-4'} border-b border-blue-600`}>
                    {isSidebarOpen && <span className="text-xl font-bold">{APP_NAME}</span>}
                    <button onClick={toggleSidebar} className="p-2 rounded-md hover:bg-blue-500 focus:outline-none focus:bg-blue-500" aria-label={isSidebarOpen ? "Collapse sidebar" : "Expand sidebar"}>
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                    </button>
                  </div>
                  <nav className="flex-grow p-2 space-y-2">
                    <NavLinkItem to="/" icon={<HomeIcon />} isSidebarOpen={isSidebarOpen}>Dashboard</NavLinkItem>
                    <NavLinkItem to="/inventory" icon={<InventoryIcon />} isSidebarOpen={isSidebarOpen}>Inventory</NavLinkItem>
                    <NavLinkItem to="/forecasting" icon={<ForecastIcon />} isSidebarOpen={isSidebarOpen}>Forecasting</NavLinkItem>
                    <NavLinkItem to="/recommendations" icon={<RecommendIcon />} isSidebarOpen={isSidebarOpen}>Recommendations</NavLinkItem>
                    {currentUser?.role === UserRole.ADMIN && (
                       <NavLinkItem to="/admin-panel" icon={<AdminIcon />} isSidebarOpen={isSidebarOpen}>Admin Panel</NavLinkItem>
                    )}
                    {/* Debug info - remove in production */}
                    {currentUser && (
                      <div className={`text-xs text-blue-200 mt-2 ${isSidebarOpen ? 'px-3' : 'text-center'}`}>
                        Role: {currentUser.role} ({currentUser.role === UserRole.ADMIN ? 'Admin' : 'Not Admin'})
                      </div>
                    )}
                  </nav>
                  <div className="p-4 border-t border-blue-600">
                    {currentUser && (
                      <div className={`flex items-center mb-3 ${isSidebarOpen ? '' : 'justify-center'}`}>
                        <UserIcon />
                        {isSidebarOpen && (
                          <div className="ml-3">
                            <p className="text-sm font-medium" title={currentUser.name}>{currentUser.name.length > 15 ? currentUser.name.substring(0,13) + "..." : currentUser.name}</p>
                            <p className="text-xs text-blue-200">{currentUser.role}</p>
                          </div>
                        )}
                      </div>
                    )}
                     <button
                        onClick={handleLogout}
                        title={isSidebarOpen ? "" : "Logout"}
                        className={`w-full flex items-center text-sm bg-red-500 hover:bg-red-600 text-white py-2 px-2 rounded-md transition-colors
                                    ${isSidebarOpen ? 'justify-start' : 'justify-center'}`}
                      >
                        <LogoutIcon />
                        {isSidebarOpen && <span className="ml-1">Logout</span>}
                      </button>
                  </div>
                </aside>

                <main className="flex-1 overflow-y-auto">
                  {/* Nested Routes for main content area */}
                  <Routes>
                    <Route path="/" element={<DashboardPage currentUser={currentUser} />} />
                    <Route path="/inventory" element={<InventoryPage currentUser={currentUser} />} />
                    <Route path="/forecasting" element={<ForecastingPage currentUser={currentUser} />} />
                    <Route path="/recommendations" element={<RecommendationsPage currentUser={currentUser} />} />
                     {currentUser?.role === UserRole.ADMIN && (
                        <Route path="/admin-panel" element={<AdminPanelPage currentUser={currentUser}/>} />
                     )}
                    {/* Fallback route for unmatched paths within the protected area */}
                    <Route path="*" element={<Navigate to="/" />} />
                  </Routes>
                </main>
              </div>
            </ProtectedRoute>
          }
        />
      </Routes>
    </HashRouter>
  );
};

export default App;
