import React, { useState, useEffect, useCallback } from 'react';
import ProductCard from './ProductCard';
import RecommendationSection from './RecommendationSection';
import Spinner from './common/Spinner';
import Alert from './common/Alert';
import { fetchProducts } from '../services/dataService';
import { getProductRecommendations, searchAnalyticsInsights } from '../services/aiService';
import { Product, User, AISearchResult } from '../types';

interface RecommendationsPageProps {
  currentUser: User | null;
}

const RecommendationsPage: React.FC<RecommendationsPageProps> = ({ currentUser }) => {
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [recommendationsFBT, setRecommendationsFBT] = useState<Product[]>([]);
  const [recommendationsCAV, setRecommendationsCAV] = useState<Product[]>([]);
  
  const [loadingProducts, setLoadingProducts] = useState<boolean>(true);
  const [loadingRecsFBT, setLoadingRecsFBT] = useState<boolean>(false);
  const [loadingRecsCAV, setLoadingRecsCAV] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<AISearchResult | null>(null);
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoadingProducts(true);
        setError(null);
        const fetchedProducts = await fetchProducts();
        setAllProducts(fetchedProducts);
      } catch (err: any) {
        console.error("Failed to load products:", err);
        setError(err.message || "Failed to load products. Please try again later.");
      } finally {
        setLoadingProducts(false);
      }
    };
    loadProducts();
  }, []);

  const fetchRecommendationsForProduct = useCallback(async (product: Product) => {
    if (!product) return;
    
    setLoadingRecsFBT(true);
    setLoadingRecsCAV(true);
    setError(null); // Clear previous errors

    try {
      const [fbt, cav] = await Promise.all([
        getProductRecommendations(product, 'frequently_bought_together'),
        getProductRecommendations(product, 'customers_also_viewed')
      ]);
      setRecommendationsFBT(fbt);
      setRecommendationsCAV(cav);
    } catch (err: any) {
      console.error("Failed to fetch recommendations:", err);
      setError(err.message || "Failed to fetch recommendations. Please try again later.");
      setRecommendationsFBT([]);
      setRecommendationsCAV([]);
    } finally {
      setLoadingRecsFBT(false);
      setLoadingRecsCAV(false);
    }
  }, []);


  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product);
    setRecommendationsFBT([]);
    setRecommendationsCAV([]);
    fetchRecommendationsForProduct(product);
  };

  const handleSearchInsights = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setLoadingSearch(true);
    setSearchResults(null);
    setError(null); // Clear previous errors
    try {
      const results = await searchAnalyticsInsights(searchQuery);
      setSearchResults(results);
    } catch (err: any) {
      console.error("Error fetching insights:", err);
      setError(err.message || "Failed to fetch insights. Please try again.");
    } finally {
      setLoadingSearch(false);
    }
  };

  return (
    <div className="p-4 md:p-6 space-y-6 bg-slate-100 min-h-screen">
      <h2 className="text-3xl font-bold text-gray-800">AI Product Recommendations & Insights</h2>
      
      {error && <Alert message={error} type="error" onClose={() => setError(null)} />}

      <div className="bg-white p-6 rounded-xl shadow-lg">
        <h3 className="text-xl font-semibold text-gray-700 mb-4">Select a Product to See Recommendations</h3>
        {loadingProducts ? <Spinner /> : (
          allProducts.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 max-h-96 overflow-y-auto p-2 border rounded-md">
              {allProducts.map(p => (
                <ProductCard key={p.id} product={p} onSelectProduct={handleSelectProduct} isSelected={selectedProduct?.id === p.id} />
              ))}
            </div>
          ) : <p className="text-gray-500">No products available.</p>
        )}
      </div>

      {selectedProduct && (
        <div className="bg-white p-6 rounded-xl shadow-lg mt-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">Recommendations for: {selectedProduct.name}</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <ProductCard product={selectedProduct} />
            </div>
            <div className="md:col-span-2 space-y-6">
              <RecommendationSection title="Frequently Bought Together" products={recommendationsFBT} loading={loadingRecsFBT} />
              <RecommendationSection title="Customers Also Viewed" products={recommendationsCAV} loading={loadingRecsCAV} />
            </div>
          </div>
        </div>
      )}
      {!selectedProduct && !loadingProducts && (
         <div className="bg-white p-6 rounded-xl shadow-lg text-center text-gray-500">
            <p>Select a product from the list above to view AI-powered recommendations.</p>
        </div>
      )}

       {currentUser?.role === 'Admin' && (
        <div className="bg-white p-6 rounded-xl shadow-lg mt-6">
          <h3 className="text-xl font-semibold text-gray-700 mb-4">Get AI-Powered Analytics Insights (Admin)</h3>
          <form onSubmit={handleSearchInsights} className="flex gap-2 mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Ask a question about your e-commerce data..."
              className="flex-grow p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
            <button 
              type="submit"
              disabled={loadingSearch || !searchQuery.trim()}
              className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:bg-gray-400"
            >
              {loadingSearch ? 'Searching...' : 'Get Insights'}
            </button>
          </form>
          {loadingSearch && <Spinner />}
          {searchResults && (
            <div className="mt-4 p-4 border border-gray-200 rounded-md bg-gray-50">
              <h4 className="font-semibold text-lg text-gray-800 mb-2">AI Insight:</h4>
              <p className="text-gray-700 whitespace-pre-wrap">{searchResults.text}</p>
              {searchResults.sources && searchResults.sources.length > 0 && (
                <div className="mt-3">
                  <h5 className="text-sm font-semibold text-gray-600">Sources:</h5>
                  <ul className="list-disc list-inside text-xs text-blue-600">
                    {searchResults.sources.map((source, index) => (
                      <li key={index}>
                        <a href={source.web.uri} target="_blank" rel="noopener noreferrer" className="hover:underline">
                          {source.web.title || source.web.uri}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecommendationsPage;