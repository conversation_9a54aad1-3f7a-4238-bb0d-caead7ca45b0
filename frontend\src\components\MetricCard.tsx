import React from 'react';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  trend?: string; // e.g., "+5.2%"
  trendColor?: string; // e.g., 'text-green-500' or 'text-red-500'
  bgColor?: string; // Tailwind background color class
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, trend, trendColor = 'text-gray-500', bgColor = 'bg-white' }) => {
  return (
    <div className={`${bgColor} p-6 rounded-xl shadow-lg flex flex-col justify-between`}>
      <div className="flex justify-between items-start">
        <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider">{title}</h4>
        {icon && <div className="text-blue-500 text-2xl">{icon}</div>}
      </div>
      <div className="mt-2">
        <p className="text-3xl font-bold text-gray-800">{value}</p>
        {trend && <p className={`text-xs mt-1 ${trendColor}`}>{trend}</p>}
      </div>
    </div>
  );
};

export default MetricCard;