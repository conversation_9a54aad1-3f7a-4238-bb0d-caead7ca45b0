import React from 'react';
import { Product } from '../types';

interface ProductCardProps {
  product: Product;
  onSelectProduct?: (product: Product) => void;
  isSelected?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onSelectProduct, isSelected }) => {
  return (
    <div 
      className={`bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl ${isSelected ? 'ring-2 ring-blue-500 scale-105' : 'hover:scale-105'} ${onSelectProduct ? 'cursor-pointer' : ''}`}
      onClick={onSelectProduct ? () => onSelectProduct(product) : undefined}
      role={onSelectProduct ? "button" : undefined}
      tabIndex={onSelectProduct ? 0 : undefined}
      onKeyDown={onSelectProduct ? (e) => (e.key === 'Enter' || e.key === ' ') && onSelectProduct(product) : undefined}
      aria-pressed={isSelected}
    >
      <img src={product.imageUrl || `https://via.placeholder.com/300x200.png?text=${encodeURIComponent(product.name)}`} alt={product.name} className="w-full h-48 object-cover"/>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-1 truncate" title={product.name}>{product.name}</h3>
        <p className="text-sm text-gray-500 mb-2">{product.category}</p>
        <p className="text-xl font-bold text-blue-600">${product.price.toFixed(2)}</p>
        {product.description && <p className="text-xs text-gray-600 mt-2 h-16 overflow-y-auto line-clamp-4">{product.description}</p>}
      </div>
    </div>
  );
};

export default ProductCard;