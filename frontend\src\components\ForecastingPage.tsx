import React, { useState, useEffect, useCallback } from 'react';
import ProductSelector from './ProductSelector';
import Forecast<PERSON>hart from './ForecastChart';
import ChartContainer from './common/ChartContainer';
import Spinner from './common/Spinner';
import Alert from './common/Alert';
import { fetchProducts, fetchHistoricalSalesForProduct } from '../services/dataService';
import { getSalesForecast } from '../services/aiService';
import { Product, ForecastDataPoint, User } from '../types';

interface ForecastingPageProps {
  currentUser: User | null;
}

const ForecastingPage: React.FC<ForecastingPageProps> = ({ currentUser }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [forecastData, setForecastData] = useState<ForecastDataPoint[]>([]);
  const [loadingProducts, setLoadingProducts] = useState<boolean>(true);
  const [loadingForecast, setLoadingForecast] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoadingProducts(true);
        setError(null);
        const fetchedProducts = await fetchProducts();
        setProducts(fetchedProducts);
        if (fetchedProducts.length > 0 && !selectedProductId) {
            // Optionally auto-select the first product
            // setSelectedProductId(fetchedProducts[0].id);
            // setSelectedProduct(fetchedProducts[0]);
        }
      } catch (err: any) {
        console.error("Failed to load products:", err);
        setError(err.message || "Failed to load products. Please try again later.");
      } finally {
        setLoadingProducts(false);
      }
    };
    loadProducts();
  }, [selectedProductId]); // Rerun if selectedProductId changes to ensure consistency, though primary load is once.
  
  const handleProductChange = (productId: string) => {
    setSelectedProductId(productId);
    setForecastData([]); 
    const product = products.find(p => p.id === productId);
    setSelectedProduct(product || null);
  };

  const generateForecast = useCallback(async () => {
    if (!selectedProductId || !selectedProduct) {
      setError("Please select a product to generate a forecast.");
      return;
    }

    try {
      setLoadingForecast(true);
      setError(null);
      const historicalSales = await fetchHistoricalSalesForProduct(selectedProductId);
      const historicalDataPoints: ForecastDataPoint[] = historicalSales.map(sale => ({
        date: sale.date.split('T')[0], // Ensure YYYY-MM-DD format
        value: sale.sales,
        type: 'actual',
      }));
      
      const aiForecastedPoints = await getSalesForecast(selectedProduct, historicalSales);
      
      const combinedData = [...historicalDataPoints, ...aiForecastedPoints.map(f => ({...f, date: f.date.split('T')[0]}))];
      // Sort combined data by date to ensure correct chart rendering
      combinedData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
      setForecastData(combinedData);

    } catch (err: any) {
      console.error("Failed to generate forecast:", err);
      setError(err.message || "Failed to generate sales forecast. Please try again.");
      setForecastData([]); 
    } finally {
      setLoadingForecast(false);
    }
  }, [selectedProductId, selectedProduct]);

  return (
    <div className="p-4 md:p-6 space-y-6 bg-slate-100 min-h-screen">
      <h2 className="text-3xl font-bold text-gray-800">AI Sales Forecasting</h2>
       {currentUser?.role !== 'Admin' && (
         <Alert type="info" message="Sales forecasting features are enhanced for Admin users." />
       )}

      {loadingProducts && <Spinner />}
      {error && <Alert message={error} type="error" onClose={() => setError(null)} />}
      
      {!loadingProducts && products.length > 0 && (
        <div className="bg-white p-6 rounded-xl shadow-lg">
          <ProductSelector
            products={products}
            selectedProductId={selectedProductId}
            onProductChange={handleProductChange}
            disabled={loadingForecast}
            label="Select Product for Forecast:"
          />
          <button
            onClick={generateForecast}
            disabled={!selectedProductId || loadingForecast}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:bg-gray-300"
          >
            {loadingForecast ? 'Generating Forecast...' : 'Generate Forecast'}
          </button>
        </div>
      )}
      {!loadingProducts && products.length === 0 && !error && (
        <div className="bg-white p-6 rounded-xl shadow-lg text-center text-gray-500">
          <p>No products available to generate forecasts. Please add products first.</p>
        </div>
      )}
      
      {loadingForecast && <div className="flex justify-center pt-10"><Spinner /></div>}

      {!loadingForecast && forecastData.length > 0 && selectedProduct && (
        <ChartContainer title={`Sales Forecast for ${selectedProduct.name}`}>
          <ForecastChart data={forecastData} />
        </ChartContainer>
      )}
      {!loadingForecast && !loadingProducts && selectedProductId && forecastData.length === 0 && !error && (
         <div className="bg-white p-6 rounded-xl shadow-lg text-center text-gray-500">
            <p>Select a product and click "Generate Forecast" to see predictions.</p>
        </div>
      )}
    </div>
  );
};

export default ForecastingPage;