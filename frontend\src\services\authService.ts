import { User } from '../types';
import { post, get } from './apiHelper';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string; 
}

export interface SessionResponse { // Backend /me might return the full user object
  user: User;
}

export const login = async (email: string, password: string): Promise<LoginResponse> => {
  return post<LoginRequest, LoginResponse>('/auth/login', { email, password });
};

export const fetchCurrentUserSession = async (token: string): Promise<User | null> => {
  if (!token) return null; 
  try {
    // The backend /auth/me route returns { user: User } structure
    const response = await get<{ user: User }>('/auth/me', token);
    
    // Debug log to check user role
    console.log('User session data:', response.user);
    
    // Ensure the role is correctly set to match the backend enum values
    if (response.user && response.user.role) {
      // Force the role to be lowercase to match backend enum
      if (response.user.role === 'Admin') {
        response.user.role = 'admin';
      } else if (response.user.role === 'Seller') {
        response.user.role = 'seller';
      }
    }
    
    return response.user;
  } catch (error: any) {
    console.error("Failed to fetch current user session:", error);
    if (error.statusCode === 401 || error.statusCode === 403) {
      // Unauthorized or Forbidden, clear local token
      localStorage.removeItem('authToken');
      localStorage.removeItem('currentUser');
      return null;
    }
    // For other errors, rethrow to be handled by the caller
    throw error; 
  }
};

// Conceptual logout: In a stateless JWT setup, backend logout might not be strictly necessary
// or might involve token blocklisting if using refresh tokens.
// For this app, client-side token removal is the primary mechanism.
export const logoutUser = async (token?: string): Promise<void> => {
  // if (token) {
  //   try {
  //     // await post('/auth/logout', {}, token); // Example if you had a backend logout endpoint
  //     console.log("Conceptual backend logout called if API existed.");
  //   } catch (error) {
  //     console.error("Error during conceptual backend logout:", error);
  //   }
  // }
  // Client-side cleanup is handled in App.tsx
  return Promise.resolve();
};