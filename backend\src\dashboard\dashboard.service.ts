import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Sale } from './entities/sale.entity';
import { Demographic } from './entities/demographic.entity';
import { ProductsService } from '../products/products.service';
import { SalesTrendData, ProductCategoryDistribution } from '../shared/types.shared';
import { DashboardMetricsDto, CustomerDemographicsResponseDto } from './dashboard.dto';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);

  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(Demographic)
    private demographicRepository: Repository<Demographic>,
    private productsService: ProductsService,
  ) {}

  async getMetrics(): Promise<DashboardMetricsDto> {
    try {
      // Get total revenue - calculate as sum of (sales * price) for each sale
      const totalRevenueResult = await this.saleRepository
        .createQueryBuilder('sale')
        .leftJoin('sale.product', 'product')
        .select('SUM(sale.sales * product.price)', 'totalRevenue')
        .getRawOne();
      
      const totalRevenue = totalRevenueResult?.totalRevenue || 0;
      
      // Get total orders
      const totalOrders = await this.saleRepository.count();
      
      // Get total customers
      const totalCustomers = await this.demographicRepository.count();
      
      this.logger.debug(`Retrieved metrics: Revenue=${totalRevenue}, Orders=${totalOrders}, Customers=${totalCustomers}`);
      
      return {
        totalRevenue: parseFloat(totalRevenue),
        totalOrders,
        totalCustomers
      };
    } catch (error) {
      this.logger.error(`Error retrieving dashboard metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSalesTrends(): Promise<SalesTrendData[]> {
    try {
      // Simplified query based on the SQL example: SELECT date, SUM(sales) as sales FROM sales GROUP BY date ORDER BY date
      const salesTrends = await this.saleRepository
        .createQueryBuilder('sale')
        .select('sale.date', 'date')
        .addSelect('SUM(sale.sales)', 'sales')
        .groupBy('sale.date')
        .orderBy('sale.date', 'ASC')
        .getRawMany();

      this.logger.debug(`Retrieved ${salesTrends.length} sales trend records`);
      
      // Format the date for display and convert sales to number
      return salesTrends.map(st => {
        // Convert the date string to a Date object if it's not already
        const dateObj = typeof st.date === 'string' ? new Date(st.date) : st.date;
        
        return {
          date: dateObj.toISOString().split('T')[0], // Format as YYYY-MM-DD
          sales: parseFloat(st.sales || '0')
        };
      });
    } catch (error) {
      this.logger.error(`Error retrieving sales trends: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getTopProducts(limit: number = 5): Promise<ProductCategoryDistribution[]> {
    try {
      // Match the SQL example: SELECT p.name, SUM(s.sales * p.price) as value FROM sales s JOIN products p ON s.product_id = p.id GROUP BY p.id, p.name ORDER BY value DESC LIMIT 5
      const topProducts = await this.saleRepository
        .createQueryBuilder('sale')
        .select('product.name', 'name')
        .addSelect('SUM(sale.sales * product.price)', 'value')
        .innerJoin('sale.product', 'product')
        .groupBy('product.id, product.name')
        .orderBy('value', 'DESC')
        .limit(limit)
        .getRawMany();

      this.logger.debug(`Retrieved ${topProducts.length} top products`);
      
      return topProducts.map(tp => ({ 
        name: tp.name, 
        value: parseFloat(tp.value || '0') 
      }));
    } catch (error) {
      this.logger.error(`Error retrieving top products: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCustomerDemographics(): Promise<CustomerDemographicsResponseDto> {
    try {
      // Age demographics
      const ageDemographics = await this.demographicRepository
        .createQueryBuilder('demographic')
        .select('demographic.age_group', 'name')
        .addSelect('COUNT(*)', 'value')
        .where('demographic.age_group IS NOT NULL')
        .groupBy('demographic.age_group')
        .orderBy('demographic.age_group')
        .getRawMany();

      // Gender demographics
      const genderDemographics = await this.demographicRepository
        .createQueryBuilder('demographic')
        .select('demographic.gender', 'name')
        .addSelect('COUNT(*)', 'value')
        .where('demographic.gender IS NOT NULL')
        .groupBy('demographic.gender')
        .orderBy('demographic.gender')
        .getRawMany();

      this.logger.debug(`Retrieved demographics: ${ageDemographics.length} age groups, ${genderDemographics.length} gender groups`);
      
      return { 
        age: ageDemographics.map(item => ({ 
          name: item.name, 
          value: parseInt(item.value) 
        })), 
        gender: genderDemographics.map(item => ({ 
          name: item.name, 
          value: parseInt(item.value) 
        })) 
      };
    } catch (error) {
      this.logger.error(`Error retrieving customer demographics: ${error.message}`, error.stack);
      throw error;
    }
  }

  async onModuleInit() {
    await this.seedDemographics();
    // Ensure products are seeded by ProductsService.onModuleInit which is called before this.
    // Then seed sales.
    await this.seedSales();
  }

  private async seedDemographics() {
    const demographicCount = await this.demographicRepository.count();
    if (demographicCount === 0) {
      this.logger.log('No demographics found. Seeding initial demographics...');
      
      const demographicsToSeed = [
        { customerId: '1', ageGroup: '25-34', gender: 'Female', location: 'New York', acquisitionChannel: 'Social Media', customerSince: new Date('2022-01-15'), totalOrders: 5, totalSpent: 250.75 },
        { customerId: '2', ageGroup: '35-44', gender: 'Male', location: 'London', acquisitionChannel: 'Search', customerSince: new Date('2021-11-20'), totalOrders: 8, totalSpent: 420.50 },
        { customerId: '3', ageGroup: '18-24', gender: 'Male', location: 'Paris', acquisitionChannel: 'Referral', customerSince: new Date('2022-03-10'), totalOrders: 3, totalSpent: 150.25 },
        { customerId: '4', ageGroup: '45-54', gender: 'Female', location: 'Berlin', acquisitionChannel: 'Direct', customerSince: new Date('2021-08-05'), totalOrders: 12, totalSpent: 780.90 },
        { customerId: '5', ageGroup: '18-24', gender: 'Other', location: 'Tokyo', acquisitionChannel: 'Social Media', customerSince: new Date('2022-02-28'), totalOrders: 2, totalSpent: 95.30 },
        { customerId: '6', ageGroup: '25-34', gender: 'Female', location: 'Chicago', acquisitionChannel: 'Email', customerSince: new Date('2021-12-12'), totalOrders: 7, totalSpent: 340.15 },
        { customerId: '7', ageGroup: '55+', gender: 'Male', location: 'New York', acquisitionChannel: 'Search', customerSince: new Date('2021-07-22'), totalOrders: 15, totalSpent: 950.60 },
      ];
      
      try {
        for (const demographic of demographicsToSeed) {
          await this.demographicRepository.save(demographic);
        }
        this.logger.log(`${demographicsToSeed.length} demographics seeded.`);
      } catch (error) {
        this.logger.error(`Error seeding demographics: ${error.message}`, error.stack);
      }
    }
  }

  private async seedSales() {
    const saleCount = await this.saleRepository.count();
    if (saleCount === 0) {
      this.logger.log('No sales found. Seeding initial sales data...');
      
      try {
        const products = await this.productsService.findAll();
        const demographics = await this.demographicRepository.find();
        
        if (products.length === 0 || demographics.length === 0) {
          this.logger.warn('Cannot seed sales: DB needs products and demographics. Ensure ProductsService seeds first.');
          return;
        }

        const salesToSeed = [];
        for (let i = 0; i < (75 + Math.floor(Math.random() * 50)); i++) { // 75-125 sales
          const product = products[Math.floor(Math.random() * products.length)];
          const demographic = demographics[Math.floor(Math.random() * demographics.length)];
          const quantity = Math.floor(Math.random() * 3) + 1;
          const saleDate = new Date();
          saleDate.setDate(saleDate.getDate() - Math.floor(Math.random() * 365)); // Sales in last year

          salesToSeed.push({
            productId: product.id,
            date: saleDate,
            sales: quantity,
            salesperson: `Salesperson ${Math.floor(Math.random() * 5) + 1}`,
            salespersonEmail: `salesperson${Math.floor(Math.random() * 5) + 1}@example.com`,
            product: product
          });
        }
        
        for (const sale of salesToSeed) {
          await this.saleRepository.save(sale);
        }
        
        this.logger.log(`${salesToSeed.length} sales seeded.`);
      } catch (error) {
        this.logger.error(`Error seeding sales: ${error.message}`, error.stack);
      }
    }
  }
}