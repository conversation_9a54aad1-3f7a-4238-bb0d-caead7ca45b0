import React from 'react';
import { Product } from '../types';

interface ProductSelectorProps {
  products: Product[];
  selectedProductId: string | null;
  onProductChange: (productId: string) => void;
  disabled?: boolean;
  label?: string;
}

const ProductSelector: React.FC<ProductSelectorProps> = ({ products, selectedProductId, onProductChange, disabled, label = "Select Product:" }) => {
  return (
    <div className="mb-4">
      <label htmlFor="product-select" className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <select
        id="product-select"
        value={selectedProductId || ''}
        onChange={(e) => onProductChange(e.target.value)}
        disabled={disabled || products.length === 0}
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm disabled:bg-gray-100"
      >
        <option value="" disabled>-- Select a Product --</option>
        {products.map((product) => (
          <option key={product.id} value={product.id}>
            {product.name}
          </option>
        ))}
      </select>
      {products.length === 0 && !disabled && <p className="text-xs text-gray-500 mt-1">No products available for selection.</p>}
    </div>
  );
};

export default ProductSelector;