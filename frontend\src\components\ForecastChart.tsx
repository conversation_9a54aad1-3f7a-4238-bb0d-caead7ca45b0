
import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer, Dot } from 'recharts'; // Import Dot
import { ForecastDataPoint } from '../types';
import { CHART_COLORS } from '../constants';

interface ForecastChartProps {
  data: ForecastDataPoint[];
}

const ForecastChart: React.FC<ForecastChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <p className="text-center text-gray-500">No forecast data available.</p>;
  }

  const actualData = data.filter(d => d.type === 'actual');
  const forecastedData = data.filter(d => d.type === 'forecasted');
  
  // To connect the lines, the last actual point needs to be the first forecasted point
  let displayForecastData = [...forecastedData];
  if (actualData.length > 0 && forecastedData.length > 0) {
      const lastActualPoint = actualData[actualData.length - 1];
      // Check if the first forecast point matches the last actual to avoid duplicate dots
      if (forecastedData[0].date !== lastActualPoint.date || forecastedData[0].value !== lastActualPoint.value) {
        displayForecastData = [lastActualPoint, ...forecastedData];
      }
  }


  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data} margin={{ top: 5, right: 30, left: 0, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
        <XAxis dataKey="date" tick={{ fontSize: 12 }} 
          tickFormatter={(dateStr) => {
            // Assuming dateStr is YYYY-MM-DD
            const dateObj = new Date(dateStr + "T00:00:00Z"); // Ensure UTC interpretation if dates are simple YYYY-MM-DD
            return dateObj.toLocaleDateString('en-US', { month: 'short', year: '2-digit', timeZone: 'UTC' });
          }}/>
        <YAxis tick={{ fontSize: 12 }} label={{ value: 'Units Sold', angle: -90, position: 'insideLeft', offset: 10, style: {fontSize: 12, fill: '#666'} }} />
        <Tooltip 
          formatter={(value: number, name: string, props: any) => [`${value} units (${props.payload.type})`, 'Sales']}
          labelFormatter={(label: string) => {
             const dateObj = new Date(label + "T00:00:00Z");
            return dateObj.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'UTC' });
          }}
        />
        <Legend wrapperStyle={{ fontSize: "14px" }} />
        <Line 
            type="monotone" 
            dataKey="value" 
            name="Actual Sales" 
            stroke={CHART_COLORS[0]} 
            strokeWidth={2} 
            activeDot={{ r: 6 }} 
            dot={({ cx, cy, stroke, payload }) => 
                payload.type === 'actual' ? <Dot cx={cx} cy={cy} r={4} fill={stroke} /> : null
            }
            connectNulls={false}
        />
        <Line 
            type="monotone" 
            dataKey="value" 
            name="Forecasted Sales" 
            stroke={CHART_COLORS[2]} 
            strokeWidth={2} 
            strokeDasharray="5 5" 
            activeDot={{ r: 6 }}
            dot={({ cx, cy, stroke, payload }) =>
                payload.type === 'forecasted' ? <Dot cx={cx} cy={cy} r={4} fill={stroke} /> : null
            }
            connectNulls={false} // Set to true if you want lines to connect over null/missing 'forecasted' points
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default ForecastChart;
