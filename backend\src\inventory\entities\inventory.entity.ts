import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryColumn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { Product } from '../../products/entities/product.entity';

@Entity('inventory')
export class Inventory {
  @PrimaryColumn('varchar')
  product_id: string;

  @Column({ type: 'integer' })
  stock_quantity: number;

  @Column({ name: 'low_stock_threshold', type: 'integer' })
  lowStockThreshold: number;

  @Column({ name: 'last_restock_date', type: 'date' })
  lastRestockDate: Date;

  // Relation to Product
  @OneToOne(() => Product)
  @JoinColumn({ name: 'product_id' })
  product: Product;
}